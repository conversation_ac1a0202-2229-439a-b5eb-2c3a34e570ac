import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../widgets/custom_button.dart';

class FlashcardsScreen extends StatelessWidget {
  const FlashcardsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Flashcards'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              _showCreateDeckDialog(context);
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          children: [
            // Search and Filter
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: 'Search flashcard decks...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: AppColors.surfaceVariant,
                    ),
                  ),
                ),
                const SizedBox(width: AppDimensions.paddingM),
                IconButton(
                  icon: const Icon(Icons.filter_list),
                  onPressed: () {
                    // TODO: Implement filter
                  },
                ),
              ],
            ),
            
            const SizedBox(height: AppDimensions.paddingL),
            
            // Quick Review Section
            _buildQuickReviewSection(context),
            
            const SizedBox(height: AppDimensions.paddingL),
            
            // Decks List
            Expanded(
              child: ListView.builder(
                itemCount: 4, // Placeholder count
                itemBuilder: (context, index) {
                  return _buildDeckCard(context, index);
                },
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showCreateDeckDialog(context);
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildQuickReviewSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Review',
              style: AppTextStyles.headline3,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Row(
              children: [
                Expanded(
                  child: _buildQuickReviewItem(
                    'Due Now',
                    '12',
                    AppColors.error,
                    Icons.schedule,
                  ),
                ),
                Expanded(
                  child: _buildQuickReviewItem(
                    'New Cards',
                    '8',
                    AppColors.secondary,
                    Icons.fiber_new,
                  ),
                ),
                Expanded(
                  child: _buildQuickReviewItem(
                    'Learning',
                    '5',
                    AppColors.accent,
                    Icons.school,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),
            CustomButton(
              text: 'Start Review Session',
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Review session feature coming soon!')),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickReviewItem(String label, String count, Color color, IconData icon) {
    return Column(
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          ),
          child: Icon(
            icon,
            color: color,
            size: AppDimensions.iconM,
          ),
        ),
        const SizedBox(height: AppDimensions.paddingS),
        Text(
          count,
          style: AppTextStyles.headline3.copyWith(color: color),
        ),
        Text(
          label,
          style: AppTextStyles.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildDeckCard(BuildContext context, int index) {
    final titles = ['Mathematics Formulas', 'Physics Constants', 'Chemistry Elements', 'History Dates'];
    final subjects = ['Mathematics', 'Physics', 'Chemistry', 'History'];
    final cardCounts = [45, 32, 67, 28];
    final dueCounts = [8, 3, 12, 5];
    final colors = [AppColors.primary, AppColors.secondary, AppColors.accent, AppColors.info];
    
    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: colors[index].withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                  child: Icon(
                    Icons.style,
                    color: colors[index],
                    size: AppDimensions.iconM,
                  ),
                ),
                const SizedBox(width: AppDimensions.paddingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        titles[index],
                        style: AppTextStyles.headline3,
                      ),
                      Text(
                        subjects[index],
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton(
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Text('Edit'),
                    ),
                    const PopupMenuItem(
                      value: 'export',
                      child: Text('Export'),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Text('Delete'),
                    ),
                  ],
                  onSelected: (value) {
                    // TODO: Handle menu actions
                  },
                ),
              ],
            ),
            
            const SizedBox(height: AppDimensions.paddingM),
            
            // Stats
            Row(
              children: [
                _buildStatChip('${cardCounts[index]} cards', AppColors.textSecondary),
                const SizedBox(width: AppDimensions.paddingS),
                _buildStatChip('${dueCounts[index]} due', AppColors.error),
                const SizedBox(width: AppDimensions.paddingS),
                _buildStatChip('85% accuracy', AppColors.success),
              ],
            ),
            
            const SizedBox(height: AppDimensions.paddingM),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: 'Study',
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Study session feature coming soon!')),
                      );
                    },
                    height: 36,
                  ),
                ),
                const SizedBox(width: AppDimensions.paddingS),
                CustomButton(
                  text: 'Browse',
                  isOutlined: true,
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Browse cards feature coming soon!')),
                    );
                  },
                  width: 80,
                  height: 36,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatChip(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingS,
        vertical: AppDimensions.paddingXS,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      ),
      child: Text(
        text,
        style: AppTextStyles.bodySmall.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  void _showCreateDeckDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create Flashcard Deck'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Choose how you want to create your flashcard deck:'),
            SizedBox(height: AppDimensions.paddingL),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('AI generation feature coming soon!')),
              );
            },
            child: const Text('Generate with AI'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Manual creation feature coming soon!')),
              );
            },
            child: const Text('Create Manually'),
          ),
        ],
      ),
    );
  }
}
