import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import 'custom_button.dart';

class EmptyStateWidget extends StatelessWidget {
  final IconData icon;
  final String title;
  final String message;
  final String? actionText;
  final VoidCallback? onAction;
  final Color? color;
  final bool showAnimation;

  const EmptyStateWidget({
    super.key,
    required this.icon,
    required this.title,
    required this.message,
    this.actionText,
    this.onAction,
    this.color,
    this.showAnimation = true,
  });

  @override
  Widget build(BuildContext context) {
    final primaryColor = color ?? AppColors.primary;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingXL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (showAnimation)
              _AnimatedIcon(
                icon: icon,
                color: primaryColor,
              )
            else
              Icon(
                icon,
                size: 80,
                color: primaryColor.withOpacity(0.6),
              ),
            
            const SizedBox(height: AppDimensions.paddingL),
            
            Text(
              title,
              style: AppTextStyles.headline2.copyWith(
                color: primaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: AppDimensions.paddingM),
            
            Text(
              message,
              style: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            
            if (actionText != null && onAction != null) ...[
              const SizedBox(height: AppDimensions.paddingXL),
              CustomButton(
                text: actionText!,
                onPressed: onAction,
                backgroundColor: primaryColor,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class _AnimatedIcon extends StatefulWidget {
  final IconData icon;
  final Color color;

  const _AnimatedIcon({
    required this.icon,
    required this.color,
  });

  @override
  State<_AnimatedIcon> createState() => _AnimatedIconState();
}

class _AnimatedIconState extends State<_AnimatedIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 0.6,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeIn,
    ));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Icon(
            widget.icon,
            size: 80,
            color: widget.color.withOpacity(_opacityAnimation.value),
          ),
        );
      },
    );
  }
}

// Predefined empty states for common scenarios
class StudyPlansEmptyState extends StatelessWidget {
  final VoidCallback? onCreatePlan;

  const StudyPlansEmptyState({
    super.key,
    this.onCreatePlan,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.book_outlined,
      title: 'No Study Plans Yet',
      message: 'Create your first study plan to get started on your learning journey. Upload a syllabus or create one manually.',
      actionText: 'Create Study Plan',
      onAction: onCreatePlan,
      color: AppColors.primary,
    );
  }
}

class FlashcardsEmptyState extends StatelessWidget {
  final VoidCallback? onCreateDeck;

  const FlashcardsEmptyState({
    super.key,
    this.onCreateDeck,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.style_outlined,
      title: 'No Flashcards Yet',
      message: 'Create your first flashcard deck to start memorizing key concepts. You can generate them with AI or create them manually.',
      actionText: 'Create Flashcard Deck',
      onAction: onCreateDeck,
      color: AppColors.secondary,
    );
  }
}

class NoCardsToReviewState extends StatelessWidget {
  final VoidCallback? onBrowseDecks;

  const NoCardsToReviewState({
    super.key,
    this.onBrowseDecks,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.check_circle_outline,
      title: 'All Caught Up!',
      message: 'Great job! You\'ve reviewed all your due flashcards. Come back later or browse your decks to study more.',
      actionText: 'Browse Decks',
      onAction: onBrowseDecks,
      color: AppColors.success,
    );
  }
}

class NoAchievementsState extends StatelessWidget {
  final VoidCallback? onStartStudying;

  const NoAchievementsState({
    super.key,
    this.onStartStudying,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.emoji_events_outlined,
      title: 'No Achievements Yet',
      message: 'Start studying to unlock your first achievement! Complete study sessions, maintain streaks, and reach milestones.',
      actionText: 'Start Studying',
      onAction: onStartStudying,
      color: AppColors.accent,
    );
  }
}

class SearchEmptyState extends StatelessWidget {
  final String searchQuery;

  const SearchEmptyState({
    super.key,
    required this.searchQuery,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.search_off,
      title: 'No Results Found',
      message: 'We couldn\'t find anything matching "$searchQuery". Try adjusting your search terms.',
      color: AppColors.textSecondary,
      showAnimation: false,
    );
  }
}

class ErrorState extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback? onRetry;

  const ErrorState({
    super.key,
    this.title = 'Something Went Wrong',
    this.message = 'We encountered an error while loading your data. Please try again.',
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.error_outline,
      title: title,
      message: message,
      actionText: onRetry != null ? 'Try Again' : null,
      onAction: onRetry,
      color: AppColors.error,
      showAnimation: false,
    );
  }
}

class OfflineState extends StatelessWidget {
  final VoidCallback? onRetry;

  const OfflineState({
    super.key,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.wifi_off,
      title: 'You\'re Offline',
      message: 'Check your internet connection and try again. Some features may not be available offline.',
      actionText: onRetry != null ? 'Retry' : null,
      onAction: onRetry,
      color: AppColors.warning,
      showAnimation: false,
    );
  }
}
