import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../controllers/flashcard_controller.dart';
import '../../controllers/user_controller.dart';
import '../../models/flashcard_model.dart';
import '../../constants/app_constants.dart';
import '../../widgets/custom_button.dart';

class FlashcardStudyScreen extends StatefulWidget {
  final FlashcardDeckModel deck;

  const FlashcardStudyScreen({
    super.key,
    required this.deck,
  });

  @override
  State<FlashcardStudyScreen> createState() => _FlashcardStudyScreenState();
}

class _FlashcardStudyScreenState extends State<FlashcardStudyScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _flipController;
  late Animation<double> _flipAnimation;
  bool _isShowingBack = false;
  bool _hasAnswered = false;

  @override
  void initState() {
    super.initState();
    _flipController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _flipAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _flipController,
      curve: Curves.easeInOut,
    ));

    // Start the study session
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final flashcardController = Provider.of<FlashcardController>(context, listen: false);
      flashcardController.startReviewSession(widget.deck);
    });
  }

  @override
  void dispose() {
    _flipController.dispose();
    super.dispose();
  }

  void _flipCard() {
    if (!_isShowingBack) {
      _flipController.forward();
      setState(() {
        _isShowingBack = true;
      });
    }
  }

  void _answerCard(CardDifficulty difficulty) async {
    if (!_hasAnswered) {
      setState(() {
        _hasAnswered = true;
      });

      final flashcardController = Provider.of<FlashcardController>(context, listen: false);
      final userController = Provider.of<UserController>(context, listen: false);

      // Answer the card
      await flashcardController.answerCard(difficulty);

      // Add points for review
      await userController.addFlashcardReview(1, AppConstants.pointsPerFlashcardReview);

      // Reset for next card
      await Future.delayed(const Duration(milliseconds: 500));
      
      if (mounted) {
        _flipController.reset();
        setState(() {
          _isShowingBack = false;
          _hasAnswered = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(widget.deck.title),
        actions: [
          Consumer<FlashcardController>(
            builder: (context, controller, child) {
              final progress = controller.reviewCards.isNotEmpty
                  ? (controller.currentCardIndex + 1) / controller.reviewCards.length
                  : 0.0;
              
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
                child: Center(
                  child: Text(
                    '${controller.currentCardIndex + 1}/${controller.reviewCards.length}',
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              );
            },
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.double.infinity, 4),
          child: Consumer<FlashcardController>(
            builder: (context, controller, child) {
              final progress = controller.reviewCards.isNotEmpty
                  ? (controller.currentCardIndex + 1) / controller.reviewCards.length
                  : 0.0;
              
              return LinearProgressIndicator(
                value: progress,
                backgroundColor: AppColors.surfaceVariant,
                valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
              );
            },
          ),
        ),
      ),
      body: Consumer<FlashcardController>(
        builder: (context, controller, child) {
          if (!controller.isStudying || controller.currentCard == null) {
            return _buildSessionComplete();
          }

          return _buildStudyInterface(controller.currentCard!);
        },
      ),
    );
  }

  Widget _buildStudyInterface(FlashcardModel card) {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      child: Column(
        children: [
          // Card
          Expanded(
            flex: 3,
            child: Center(
              child: GestureDetector(
                onTap: _flipCard,
                child: AnimatedBuilder(
                  animation: _flipAnimation,
                  builder: (context, child) {
                    final isShowingFront = _flipAnimation.value < 0.5;
                    return Transform(
                      alignment: Alignment.center,
                      transform: Matrix4.identity()
                        ..setEntry(3, 2, 0.001)
                        ..rotateY(_flipAnimation.value * 3.14159),
                      child: Card(
                        elevation: 8,
                        child: Container(
                          width: double.infinity,
                          height: 300,
                          padding: const EdgeInsets.all(AppDimensions.paddingL),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                AppColors.primary.withOpacity(0.1),
                                AppColors.secondary.withOpacity(0.1),
                              ],
                            ),
                          ),
                          child: isShowingFront
                              ? _buildCardFront(card)
                              : Transform(
                                  alignment: Alignment.center,
                                  transform: Matrix4.identity()..rotateY(3.14159),
                                  child: _buildCardBack(card),
                                ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),

          const SizedBox(height: AppDimensions.paddingL),

          // Instructions
          if (!_isShowingBack)
            Text(
              'Tap the card to reveal the answer',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            )
          else
            Text(
              'How well did you know this?',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),

          const SizedBox(height: AppDimensions.paddingL),

          // Answer Buttons
          if (_isShowingBack && !_hasAnswered)
            Expanded(
              flex: 1,
              child: _buildAnswerButtons(),
            ),
        ],
      ),
    );
  }

  Widget _buildCardFront(FlashcardModel card) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.help_outline,
          size: AppDimensions.iconL,
          color: AppColors.primary,
        ),
        const SizedBox(height: AppDimensions.paddingM),
        Text(
          card.front,
          style: AppTextStyles.headline2,
          textAlign: TextAlign.center,
        ),
        if (card.hint != null) ...[
          const SizedBox(height: AppDimensions.paddingM),
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: AppColors.info.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  size: AppDimensions.iconS,
                  color: AppColors.info,
                ),
                const SizedBox(width: AppDimensions.paddingXS),
                Flexible(
                  child: Text(
                    card.hint!,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.info,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildCardBack(FlashcardModel card) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.check_circle_outline,
          size: AppDimensions.iconL,
          color: AppColors.success,
        ),
        const SizedBox(height: AppDimensions.paddingM),
        Text(
          card.back,
          style: AppTextStyles.headline2,
          textAlign: TextAlign.center,
        ),
        if (card.tags != null && card.tags!.isNotEmpty) ...[
          const SizedBox(height: AppDimensions.paddingM),
          Wrap(
            spacing: AppDimensions.paddingXS,
            children: card.tags!.map((tag) => Chip(
              label: Text(
                tag,
                style: AppTextStyles.bodySmall,
              ),
              backgroundColor: AppColors.surfaceVariant,
            )).toList(),
          ),
        ],
      ],
    );
  }

  Widget _buildAnswerButtons() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: CustomButton(
                text: 'Again',
                backgroundColor: AppColors.error,
                onPressed: () => _answerCard(CardDifficulty.again),
                height: AppDimensions.buttonHeightL,
              ),
            ),
            const SizedBox(width: AppDimensions.paddingS),
            Expanded(
              child: CustomButton(
                text: 'Hard',
                backgroundColor: AppColors.warning,
                onPressed: () => _answerCard(CardDifficulty.hard),
                height: AppDimensions.buttonHeightL,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.paddingS),
        Row(
          children: [
            Expanded(
              child: CustomButton(
                text: 'Good',
                backgroundColor: AppColors.primary,
                onPressed: () => _answerCard(CardDifficulty.medium),
                height: AppDimensions.buttonHeightL,
              ),
            ),
            const SizedBox(width: AppDimensions.paddingS),
            Expanded(
              child: CustomButton(
                text: 'Easy',
                backgroundColor: AppColors.success,
                onPressed: () => _answerCard(CardDifficulty.easy),
                height: AppDimensions.buttonHeightL,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSessionComplete() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.celebration,
              size: 80,
              color: AppColors.success,
            ),
            const SizedBox(height: AppDimensions.paddingL),
            Text(
              'Session Complete!',
              style: AppTextStyles.headline1.copyWith(
                color: AppColors.success,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Text(
              'Great job! You\'ve completed your flashcard review session.',
              style: AppTextStyles.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.paddingXL),
            CustomButton(
              text: 'Back to Flashcards',
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        ),
      ),
    );
  }
}
