import 'package:hive/hive.dart';

part 'user_model.g.dart';

@HiveType(typeId: 0)
class UserModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String email;

  @HiveField(2)
  String? fullName;

  @HiveField(3)
  String? avatarUrl;

  @HiveField(4)
  DateTime createdAt;

  @HiveField(5)
  DateTime? lastLoginAt;

  @HiveField(6)
  UserPreferences preferences;

  @HiveField(7)
  UserStats stats;

  UserModel({
    required this.id,
    required this.email,
    this.fullName,
    this.avatarUrl,
    required this.createdAt,
    this.lastLoginAt,
    required this.preferences,
    required this.stats,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      email: json['email'] as String,
      fullName: json['full_name'] as String?,
      avatarUrl: json['avatar_url'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastLoginAt: json['last_login_at'] != null
          ? DateTime.parse(json['last_login_at'] as String)
          : null,
      preferences: UserPreferences.fromJson(
        json['preferences'] as Map<String, dynamic>? ?? {},
      ),
      stats: UserStats.fromJson(
        json['stats'] as Map<String, dynamic>? ?? {},
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'full_name': fullName,
      'avatar_url': avatarUrl,
      'created_at': createdAt.toIso8601String(),
      'last_login_at': lastLoginAt?.toIso8601String(),
      'preferences': preferences.toJson(),
      'stats': stats.toJson(),
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? fullName,
    String? avatarUrl,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    UserPreferences? preferences,
    UserStats? stats,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      preferences: preferences ?? this.preferences,
      stats: stats ?? this.stats,
    );
  }
}

@HiveType(typeId: 1)
class UserPreferences extends HiveObject {
  @HiveField(0)
  bool notificationsEnabled;

  @HiveField(1)
  TimeOfDay studyReminderTime;

  @HiveField(2)
  int dailyStudyGoalMinutes;

  @HiveField(3)
  List<int> studyDays; // 1-7 for Monday-Sunday

  @HiveField(4)
  String preferredStudyPace; // 'slow', 'medium', 'fast'

  @HiveField(5)
  bool gamificationEnabled;

  @HiveField(6)
  bool soundEnabled;

  @HiveField(7)
  String theme; // 'light', 'dark', 'system'

  UserPreferences({
    this.notificationsEnabled = true,
    this.studyReminderTime = const TimeOfDay(hour: 19, minute: 0),
    this.dailyStudyGoalMinutes = 60,
    this.studyDays = const [1, 2, 3, 4, 5], // Monday to Friday by default
    this.preferredStudyPace = 'medium',
    this.gamificationEnabled = true,
    this.soundEnabled = true,
    this.theme = 'system',
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      notificationsEnabled: json['notifications_enabled'] as bool? ?? true,
      studyReminderTime: TimeOfDay(
        hour: json['study_reminder_hour'] as int? ?? 19,
        minute: json['study_reminder_minute'] as int? ?? 0,
      ),
      dailyStudyGoalMinutes: json['daily_study_goal_minutes'] as int? ?? 60,
      studyDays: (json['study_days'] as List<dynamic>?)
              ?.map((e) => e as int)
              .toList() ??
          [1, 2, 3, 4, 5],
      preferredStudyPace: json['preferred_study_pace'] as String? ?? 'medium',
      gamificationEnabled: json['gamification_enabled'] as bool? ?? true,
      soundEnabled: json['sound_enabled'] as bool? ?? true,
      theme: json['theme'] as String? ?? 'system',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'notifications_enabled': notificationsEnabled,
      'study_reminder_hour': studyReminderTime.hour,
      'study_reminder_minute': studyReminderTime.minute,
      'daily_study_goal_minutes': dailyStudyGoalMinutes,
      'study_days': studyDays,
      'preferred_study_pace': preferredStudyPace,
      'gamification_enabled': gamificationEnabled,
      'sound_enabled': soundEnabled,
      'theme': theme,
    };
  }
}

@HiveType(typeId: 2)
class UserStats extends HiveObject {
  @HiveField(0)
  int totalPoints;

  @HiveField(1)
  int currentStreak;

  @HiveField(2)
  int longestStreak;

  @HiveField(3)
  int totalStudyTimeMinutes;

  @HiveField(4)
  int totalFlashcardsReviewed;

  @HiveField(5)
  int totalStudyPlansCompleted;

  @HiveField(6)
  DateTime? lastStudyDate;

  @HiveField(7)
  List<String> achievements;

  UserStats({
    this.totalPoints = 0,
    this.currentStreak = 0,
    this.longestStreak = 0,
    this.totalStudyTimeMinutes = 0,
    this.totalFlashcardsReviewed = 0,
    this.totalStudyPlansCompleted = 0,
    this.lastStudyDate,
    this.achievements = const [],
  });

  factory UserStats.fromJson(Map<String, dynamic> json) {
    return UserStats(
      totalPoints: json['total_points'] as int? ?? 0,
      currentStreak: json['current_streak'] as int? ?? 0,
      longestStreak: json['longest_streak'] as int? ?? 0,
      totalStudyTimeMinutes: json['total_study_time_minutes'] as int? ?? 0,
      totalFlashcardsReviewed: json['total_flashcards_reviewed'] as int? ?? 0,
      totalStudyPlansCompleted: json['total_study_plans_completed'] as int? ?? 0,
      lastStudyDate: json['last_study_date'] != null
          ? DateTime.parse(json['last_study_date'] as String)
          : null,
      achievements: (json['achievements'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_points': totalPoints,
      'current_streak': currentStreak,
      'longest_streak': longestStreak,
      'total_study_time_minutes': totalStudyTimeMinutes,
      'total_flashcards_reviewed': totalFlashcardsReviewed,
      'total_study_plans_completed': totalStudyPlansCompleted,
      'last_study_date': lastStudyDate?.toIso8601String(),
      'achievements': achievements,
    };
  }
}

// Helper class for TimeOfDay serialization
@HiveType(typeId: 3)
class TimeOfDay extends HiveObject {
  @HiveField(0)
  final int hour;

  @HiveField(1)
  final int minute;

  const TimeOfDay({required this.hour, required this.minute});

  @override
  String toString() {
    final h = hour.toString().padLeft(2, '0');
    final m = minute.toString().padLeft(2, '0');
    return '$h:$m';
  }

  @override
  bool operator ==(Object other) {
    return other is TimeOfDay && other.hour == hour && other.minute == minute;
  }

  @override
  int get hashCode => Object.hash(hour, minute);
}
