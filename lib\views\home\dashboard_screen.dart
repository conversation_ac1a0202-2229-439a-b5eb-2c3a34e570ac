import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/user_controller.dart';
import '../../models/user_model.dart';
import '../../constants/app_constants.dart';
import '../../widgets/custom_button.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Safe<PERSON>rea(
        child: Consumer2<AuthController, UserController>(
          builder: (context, authController, userController, child) {
            final user = authController.currentUser;
            final stats = userController.stats;

            return SingleChildScrollView(
              padding: const EdgeInsets.all(AppDimensions.paddingL),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  _buildHeader(user?.fullName ?? 'Student'),

                  const SizedBox(height: AppDimensions.paddingL),

                  // Quick Stats
                  _buildQuickStats(stats),

                  const SizedBox(height: AppDimensions.paddingL),

                  // Today's Goals
                  _buildTodaysGoals(),

                  const SizedBox(height: AppDimensions.paddingL),

                  // Recent Activity
                  _buildRecentActivity(),

                  const SizedBox(height: AppDimensions.paddingL),

                  // Quick Actions
                  _buildQuickActions(context),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildHeader(String userName) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Welcome back,', style: AppTextStyles.bodyMedium),
              Text(userName, style: AppTextStyles.headline2),
            ],
          ),
        ),
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          ),
          child: const Icon(
            Icons.person,
            color: Colors.white,
            size: AppDimensions.iconL,
          ),
        ),
      ],
    );
  }

  Widget _buildQuickStats(UserStats stats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Your Progress', style: AppTextStyles.headline3),
            const SizedBox(height: AppDimensions.paddingM),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Study Streak',
                    '${stats.currentStreak} days',
                    Icons.local_fire_department,
                    AppColors.accent,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Total Points',
                    '${stats.totalPoints}',
                    Icons.stars,
                    AppColors.secondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Study Time',
                    '${(stats.totalStudyTimeMinutes / 60).toStringAsFixed(1)}h',
                    Icons.access_time,
                    AppColors.primary,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Flashcards',
                    '${stats.totalFlashcardsReviewed}',
                    Icons.style,
                    AppColors.info,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          ),
          child: Icon(icon, color: color, size: AppDimensions.iconM),
        ),
        const SizedBox(height: AppDimensions.paddingS),
        Text(value, style: AppTextStyles.headline3.copyWith(color: color)),
        Text(
          label,
          style: AppTextStyles.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildTodaysGoals() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Today\'s Goals', style: AppTextStyles.headline3),
            const SizedBox(height: AppDimensions.paddingM),
            _buildGoalItem('Complete 30 minutes of study', 0.6, true),
            const SizedBox(height: AppDimensions.paddingS),
            _buildGoalItem('Review 10 flashcards', 0.8, true),
            const SizedBox(height: AppDimensions.paddingS),
            _buildGoalItem('Read Chapter 5', 0.0, false),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalItem(String title, double progress, bool isCompleted) {
    return Row(
      children: [
        Icon(
          isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
          color: isCompleted ? AppColors.success : AppColors.textTertiary,
          size: AppDimensions.iconS,
        ),
        const SizedBox(width: AppDimensions.paddingS),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  decoration: isCompleted ? TextDecoration.lineThrough : null,
                  color:
                      isCompleted
                          ? AppColors.textTertiary
                          : AppColors.textPrimary,
                ),
              ),
              if (progress > 0 && !isCompleted) ...[
                const SizedBox(height: AppDimensions.paddingXS),
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: AppColors.surfaceVariant,
                  valueColor: const AlwaysStoppedAnimation<Color>(
                    AppColors.primary,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRecentActivity() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Recent Activity', style: AppTextStyles.headline3),
            const SizedBox(height: AppDimensions.paddingM),
            _buildActivityItem(
              'Completed Mathematics Study Session',
              '2 hours ago',
              Icons.book,
              AppColors.success,
            ),
            _buildActivityItem(
              'Reviewed Physics Flashcards',
              '4 hours ago',
              Icons.style,
              AppColors.info,
            ),
            _buildActivityItem(
              'Created new study plan',
              'Yesterday',
              Icons.add_circle,
              AppColors.secondary,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(
    String title,
    String time,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingS),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
            child: Icon(icon, color: color, size: AppDimensions.iconS),
          ),
          const SizedBox(width: AppDimensions.paddingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: AppTextStyles.bodyMedium),
                Text(time, style: AppTextStyles.bodySmall),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Quick Actions', style: AppTextStyles.headline3),
        const SizedBox(height: AppDimensions.paddingM),
        Row(
          children: [
            Expanded(
              child: CustomButton(
                text: 'Start Study Session',
                icon: Icons.play_arrow,
                onPressed: () {
                  // TODO: Navigate to study session
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Study session feature coming soon!'),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: AppDimensions.paddingM),
            Expanded(
              child: CustomButton(
                text: 'Review Flashcards',
                icon: Icons.style,
                isOutlined: true,
                onPressed: () {
                  // TODO: Navigate to flashcard review
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Flashcard review feature coming soon!'),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ],
    );
  }
}
