import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../controllers/study_plan_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../services/file_service.dart';
import '../../constants/app_constants.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';

class CreateStudyPlanScreen extends StatefulWidget {
  const CreateStudyPlanScreen({super.key});

  @override
  State<CreateStudyPlanScreen> createState() => _CreateStudyPlanScreenState();
}

class _CreateStudyPlanScreenState extends State<CreateStudyPlanScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();
  
  // Form controllers
  final _titleController = TextEditingController();
  final _subjectController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  // Form data
  DateTime _startDate = DateTime.now();
  DateTime _endDate = DateTime.now().add(const Duration(days: 30));
  DateTime? _examDate;
  String _studyPace = 'medium';
  int _dailyStudyHours = 2;
  
  // File upload
  FilePickResult? _selectedFile;
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _titleController.dispose();
    _subjectController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Create Study Plan'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Upload Syllabus'),
            Tab(text: 'Manual Creation'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildSyllabusUploadTab(),
          _buildManualCreationTab(),
        ],
      ),
    );
  }

  Widget _buildSyllabusUploadTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Upload Syllabus',
              style: AppTextStyles.headline2,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            Text(
              'Upload your course syllabus and let AI create a personalized study plan for you.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            
            const SizedBox(height: AppDimensions.paddingL),
            
            // File Upload Section
            _buildFileUploadSection(),
            
            const SizedBox(height: AppDimensions.paddingL),
            
            // Basic Information
            CustomTextField(
              controller: _titleController,
              label: 'Study Plan Title (Optional)',
              hint: 'Leave empty to use course title from syllabus',
            ),
            
            const SizedBox(height: AppDimensions.paddingM),
            
            CustomTextField(
              controller: _subjectController,
              label: 'Subject (Optional)',
              hint: 'Leave empty to detect from syllabus',
            ),
            
            const SizedBox(height: AppDimensions.paddingL),
            
            // Date Selection
            _buildDateSelection(),
            
            const SizedBox(height: AppDimensions.paddingL),
            
            // Study Preferences
            _buildStudyPreferences(),
            
            const SizedBox(height: AppDimensions.paddingXL),
            
            // Create Button
            Consumer<StudyPlanController>(
              builder: (context, controller, child) {
                return CustomButton(
                  text: 'Generate Study Plan',
                  onPressed: _selectedFile != null && !controller.isLoading
                      ? _createStudyPlanFromSyllabus
                      : null,
                  isLoading: controller.isLoading,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildManualCreationTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Manual Creation',
              style: AppTextStyles.headline2,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            Text(
              'Create a study plan manually by providing the basic information.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            
            const SizedBox(height: AppDimensions.paddingL),
            
            // Required Information
            CustomTextField(
              controller: _titleController,
              label: 'Study Plan Title *',
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a title';
                }
                return null;
              },
            ),
            
            const SizedBox(height: AppDimensions.paddingM),
            
            CustomTextField(
              controller: _subjectController,
              label: 'Subject *',
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a subject';
                }
                return null;
              },
            ),
            
            const SizedBox(height: AppDimensions.paddingM),
            
            CustomTextField(
              controller: _descriptionController,
              label: 'Description (Optional)',
              maxLines: 3,
              hint: 'Brief description of what you\'ll be studying',
            ),
            
            const SizedBox(height: AppDimensions.paddingL),
            
            // Date Selection
            _buildDateSelection(),
            
            const SizedBox(height: AppDimensions.paddingXL),
            
            // Create Button
            Consumer<StudyPlanController>(
              builder: (context, controller, child) {
                return CustomButton(
                  text: 'Create Study Plan',
                  onPressed: !controller.isLoading ? _createManualStudyPlan : null,
                  isLoading: controller.isLoading,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFileUploadSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          children: [
            if (_selectedFile == null) ...[
              Icon(
                Icons.cloud_upload_outlined,
                size: 64,
                color: AppColors.textTertiary,
              ),
              const SizedBox(height: AppDimensions.paddingM),
              Text(
                'Upload Syllabus File',
                style: AppTextStyles.headline3,
              ),
              const SizedBox(height: AppDimensions.paddingS),
              Text(
                'Supported formats: PDF, DOC, DOCX, TXT\nMax size: ${AppConstants.maxFileSizeMB}MB',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppDimensions.paddingL),
              CustomButton(
                text: 'Choose File',
                onPressed: _pickFile,
                isOutlined: true,
              ),
            ] else ...[
              Row(
                children: [
                  Icon(
                    Icons.description,
                    color: AppColors.success,
                    size: AppDimensions.iconL,
                  ),
                  const SizedBox(width: AppDimensions.paddingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _selectedFile!.name,
                          style: AppTextStyles.bodyLarge.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          FileService.instance.getFileInfo(_selectedFile!)['sizeFormatted'],
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () {
                      setState(() {
                        _selectedFile = null;
                      });
                    },
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDateSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Study Period',
          style: AppTextStyles.headline3,
        ),
        const SizedBox(height: AppDimensions.paddingM),
        Row(
          children: [
            Expanded(
              child: _buildDateField(
                'Start Date',
                _startDate,
                (date) => setState(() => _startDate = date),
              ),
            ),
            const SizedBox(width: AppDimensions.paddingM),
            Expanded(
              child: _buildDateField(
                'End Date',
                _endDate,
                (date) => setState(() => _endDate = date),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.paddingM),
        _buildDateField(
          'Exam Date (Optional)',
          _examDate,
          (date) => setState(() => _examDate = date),
          isOptional: true,
        ),
      ],
    );
  }

  Widget _buildDateField(
    String label,
    DateTime? date,
    Function(DateTime) onDateSelected, {
    bool isOptional = false,
  }) {
    return InkWell(
      onTap: () async {
        final selectedDate = await showDatePicker(
          context: context,
          initialDate: date ?? DateTime.now(),
          firstDate: DateTime.now().subtract(const Duration(days: 30)),
          lastDate: DateTime.now().add(const Duration(days: 365)),
        );
        if (selectedDate != null) {
          onDateSelected(selectedDate);
        }
      },
      child: Container(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.surfaceVariant),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingXS),
            Text(
              date != null
                  ? '${date.day}/${date.month}/${date.year}'
                  : isOptional
                      ? 'Not set'
                      : 'Select date',
              style: AppTextStyles.bodyLarge,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStudyPreferences() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Study Preferences',
          style: AppTextStyles.headline3,
        ),
        const SizedBox(height: AppDimensions.paddingM),
        
        // Study Pace
        Text(
          'Study Pace',
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.paddingS),
        Row(
          children: [
            Expanded(
              child: _buildPaceOption('Relaxed', 'slow'),
            ),
            const SizedBox(width: AppDimensions.paddingS),
            Expanded(
              child: _buildPaceOption('Moderate', 'medium'),
            ),
            const SizedBox(width: AppDimensions.paddingS),
            Expanded(
              child: _buildPaceOption('Intensive', 'fast'),
            ),
          ],
        ),
        
        const SizedBox(height: AppDimensions.paddingM),
        
        // Daily Study Hours
        Text(
          'Daily Study Hours: $_dailyStudyHours hours',
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        Slider(
          value: _dailyStudyHours.toDouble(),
          min: 1,
          max: 8,
          divisions: 7,
          onChanged: (value) {
            setState(() {
              _dailyStudyHours = value.round();
            });
          },
        ),
      ],
    );
  }

  Widget _buildPaceOption(String label, String value) {
    final isSelected = _studyPace == value;
    return GestureDetector(
      onTap: () => setState(() => _studyPace = value),
      child: Container(
        padding: const EdgeInsets.symmetric(
          vertical: AppDimensions.paddingS,
          horizontal: AppDimensions.paddingM,
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : AppColors.surfaceVariant,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        child: Text(
          label,
          style: AppTextStyles.bodyMedium.copyWith(
            color: isSelected ? Colors.white : AppColors.textPrimary,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Future<void> _pickFile() async {
    try {
      final file = await FileService.instance.pickFile();
      if (file != null) {
        setState(() {
          _selectedFile = file;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking file: $e')),
        );
      }
    }
  }

  Future<void> _createStudyPlanFromSyllabus() async {
    if (_selectedFile == null) return;

    final authController = Provider.of<AuthController>(context, listen: false);
    final studyPlanController = Provider.of<StudyPlanController>(context, listen: false);

    final userId = authController.currentUser?.id;
    if (userId == null) return;

    final studyPlan = await studyPlanController.createStudyPlanFromSyllabus(
      userId: userId,
      syllabusFile: _selectedFile!,
      title: _titleController.text.trim(),
      subject: _subjectController.text.trim(),
      startDate: _startDate,
      endDate: _endDate,
      examDate: _examDate,
      studyPace: _studyPace,
      dailyStudyHours: _dailyStudyHours,
    );

    if (mounted) {
      if (studyPlan != null) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Study plan created successfully!')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(studyPlanController.errorMessage ?? 'Failed to create study plan'),
          ),
        );
      }
    }
  }

  Future<void> _createManualStudyPlan() async {
    if (!_formKey.currentState!.validate()) return;

    final authController = Provider.of<AuthController>(context, listen: false);
    final studyPlanController = Provider.of<StudyPlanController>(context, listen: false);

    final userId = authController.currentUser?.id;
    if (userId == null) return;

    final studyPlan = await studyPlanController.createManualStudyPlan(
      userId: userId,
      title: _titleController.text.trim(),
      subject: _subjectController.text.trim(),
      description: _descriptionController.text.trim().isNotEmpty 
          ? _descriptionController.text.trim() 
          : null,
      startDate: _startDate,
      endDate: _endDate,
      examDate: _examDate,
    );

    if (mounted) {
      if (studyPlan != null) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Study plan created successfully!')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(studyPlanController.errorMessage ?? 'Failed to create study plan'),
          ),
        );
      }
    }
  }
}
