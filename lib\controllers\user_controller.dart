import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../constants/app_constants.dart';

class UserController extends ChangeNotifier {
  UserPreferences _preferences = UserPreferences();
  UserStats _stats = UserStats();
  bool _isLoading = false;

  // Getters
  UserPreferences get preferences => _preferences;
  UserStats get stats => _stats;
  bool get isLoading => _isLoading;

  UserController() {
    _loadLocalData();
  }

  // Load data from local storage
  Future<void> _loadLocalData() async {
    try {
      _isLoading = true;
      notifyListeners();

      final prefs = await SharedPreferences.getInstance();
      
      // Load user preferences
      final prefsJson = prefs.getString(AppConstants.userPrefsKey);
      if (prefsJson != null) {
        // In a real implementation, you'd parse JSON here
        // For now, we'll use default preferences
      }

      // Load study streak
      final streak = prefs.getInt(AppConstants.studyStreakKey) ?? 0;
      final lastStudyDateStr = prefs.getString(AppConstants.lastStudyDateKey);
      DateTime? lastStudyDate;
      if (lastStudyDateStr != null) {
        lastStudyDate = DateTime.parse(lastStudyDateStr);
      }

      _stats = _stats.copyWith(
        currentStreak: streak,
        lastStudyDate: lastStudyDate,
      );

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Save data to local storage
  Future<void> _saveLocalData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setInt(AppConstants.studyStreakKey, _stats.currentStreak);
      if (_stats.lastStudyDate != null) {
        await prefs.setString(
          AppConstants.lastStudyDateKey,
          _stats.lastStudyDate!.toIso8601String(),
        );
      }
    } catch (e) {
      // Handle error silently for local storage
    }
  }

  // Update user preferences
  Future<void> updatePreferences(UserPreferences newPreferences) async {
    _preferences = newPreferences;
    notifyListeners();
    await _saveLocalData();
  }

  // Update specific preference
  Future<void> updateNotificationSettings(bool enabled) async {
    _preferences = UserPreferences(
      notificationsEnabled: enabled,
      studyReminderTime: _preferences.studyReminderTime,
      dailyStudyGoalMinutes: _preferences.dailyStudyGoalMinutes,
      studyDays: _preferences.studyDays,
      preferredStudyPace: _preferences.preferredStudyPace,
      gamificationEnabled: _preferences.gamificationEnabled,
      soundEnabled: _preferences.soundEnabled,
      theme: _preferences.theme,
    );
    notifyListeners();
    await _saveLocalData();
  }

  Future<void> updateStudyGoal(int minutes) async {
    _preferences = UserPreferences(
      notificationsEnabled: _preferences.notificationsEnabled,
      studyReminderTime: _preferences.studyReminderTime,
      dailyStudyGoalMinutes: minutes,
      studyDays: _preferences.studyDays,
      preferredStudyPace: _preferences.preferredStudyPace,
      gamificationEnabled: _preferences.gamificationEnabled,
      soundEnabled: _preferences.soundEnabled,
      theme: _preferences.theme,
    );
    notifyListeners();
    await _saveLocalData();
  }

  Future<void> updateStudyDays(List<int> days) async {
    _preferences = UserPreferences(
      notificationsEnabled: _preferences.notificationsEnabled,
      studyReminderTime: _preferences.studyReminderTime,
      dailyStudyGoalMinutes: _preferences.dailyStudyGoalMinutes,
      studyDays: days,
      preferredStudyPace: _preferences.preferredStudyPace,
      gamificationEnabled: _preferences.gamificationEnabled,
      soundEnabled: _preferences.soundEnabled,
      theme: _preferences.theme,
    );
    notifyListeners();
    await _saveLocalData();
  }

  // Update user stats
  Future<void> addStudySession(int durationMinutes, int pointsEarned) async {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final lastStudyDay = _stats.lastStudyDate != null
        ? DateTime(_stats.lastStudyDate!.year, _stats.lastStudyDate!.month, _stats.lastStudyDate!.day)
        : null;

    // Update streak
    int newStreak = _stats.currentStreak;
    if (lastStudyDay == null) {
      // First study session
      newStreak = 1;
    } else if (lastStudyDay == today) {
      // Same day, don't change streak
      newStreak = _stats.currentStreak;
    } else if (lastStudyDay == today.subtract(const Duration(days: 1))) {
      // Consecutive day
      newStreak = _stats.currentStreak + 1;
    } else {
      // Streak broken
      newStreak = 1;
    }

    _stats = UserStats(
      totalPoints: _stats.totalPoints + pointsEarned,
      currentStreak: newStreak,
      longestStreak: newStreak > _stats.longestStreak ? newStreak : _stats.longestStreak,
      totalStudyTimeMinutes: _stats.totalStudyTimeMinutes + durationMinutes,
      totalFlashcardsReviewed: _stats.totalFlashcardsReviewed,
      totalStudyPlansCompleted: _stats.totalStudyPlansCompleted,
      lastStudyDate: now,
      achievements: _stats.achievements,
    );

    notifyListeners();
    await _saveLocalData();
    _checkForNewAchievements();
  }

  Future<void> addFlashcardReview(int count, int pointsEarned) async {
    _stats = UserStats(
      totalPoints: _stats.totalPoints + pointsEarned,
      currentStreak: _stats.currentStreak,
      longestStreak: _stats.longestStreak,
      totalStudyTimeMinutes: _stats.totalStudyTimeMinutes,
      totalFlashcardsReviewed: _stats.totalFlashcardsReviewed + count,
      totalStudyPlansCompleted: _stats.totalStudyPlansCompleted,
      lastStudyDate: _stats.lastStudyDate,
      achievements: _stats.achievements,
    );

    notifyListeners();
    await _saveLocalData();
    _checkForNewAchievements();
  }

  Future<void> completeStudyPlan() async {
    _stats = UserStats(
      totalPoints: _stats.totalPoints + AppConstants.pointsPerWeekStreak,
      currentStreak: _stats.currentStreak,
      longestStreak: _stats.longestStreak,
      totalStudyTimeMinutes: _stats.totalStudyTimeMinutes,
      totalFlashcardsReviewed: _stats.totalFlashcardsReviewed,
      totalStudyPlansCompleted: _stats.totalStudyPlansCompleted + 1,
      lastStudyDate: _stats.lastStudyDate,
      achievements: _stats.achievements,
    );

    notifyListeners();
    await _saveLocalData();
    _checkForNewAchievements();
  }

  // Check for new achievements
  void _checkForNewAchievements() {
    final newAchievements = <String>[];

    // First study session
    if (_stats.totalStudyTimeMinutes >= 30 && !_stats.achievements.contains('first_session')) {
      newAchievements.add('first_session');
    }

    // Study streak achievements
    if (_stats.currentStreak >= 7 && !_stats.achievements.contains('week_streak')) {
      newAchievements.add('week_streak');
    }
    if (_stats.currentStreak >= 30 && !_stats.achievements.contains('month_streak')) {
      newAchievements.add('month_streak');
    }

    // Flashcard achievements
    if (_stats.totalFlashcardsReviewed >= 100 && !_stats.achievements.contains('flashcard_master')) {
      newAchievements.add('flashcard_master');
    }

    // Study time achievements
    if (_stats.totalStudyTimeMinutes >= 600 && !_stats.achievements.contains('study_warrior')) { // 10 hours
      newAchievements.add('study_warrior');
    }

    // Points achievements
    if (_stats.totalPoints >= 1000 && !_stats.achievements.contains('point_collector')) {
      newAchievements.add('point_collector');
    }

    if (newAchievements.isNotEmpty) {
      _stats = UserStats(
        totalPoints: _stats.totalPoints,
        currentStreak: _stats.currentStreak,
        longestStreak: _stats.longestStreak,
        totalStudyTimeMinutes: _stats.totalStudyTimeMinutes,
        totalFlashcardsReviewed: _stats.totalFlashcardsReviewed,
        totalStudyPlansCompleted: _stats.totalStudyPlansCompleted,
        lastStudyDate: _stats.lastStudyDate,
        achievements: [..._stats.achievements, ...newAchievements],
      );
      notifyListeners();
    }
  }

  // Get achievement details
  Map<String, String> getAchievementDetails(String achievementId) {
    switch (achievementId) {
      case 'first_session':
        return {'title': 'Getting Started', 'description': 'Complete your first study session'};
      case 'week_streak':
        return {'title': 'Week Warrior', 'description': 'Study for 7 consecutive days'};
      case 'month_streak':
        return {'title': 'Monthly Master', 'description': 'Study for 30 consecutive days'};
      case 'flashcard_master':
        return {'title': 'Flashcard Master', 'description': 'Review 100 flashcards'};
      case 'study_warrior':
        return {'title': 'Study Warrior', 'description': 'Complete 10 hours of study time'};
      case 'point_collector':
        return {'title': 'Point Collector', 'description': 'Earn 1000 points'};
      default:
        return {'title': 'Achievement', 'description': 'You earned an achievement!'};
    }
  }

  // Reset stats (for testing or user request)
  Future<void> resetStats() async {
    _stats = UserStats();
    notifyListeners();
    await _saveLocalData();
  }
}
