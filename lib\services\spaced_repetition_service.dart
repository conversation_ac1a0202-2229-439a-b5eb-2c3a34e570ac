import '../models/flashcard_model.dart';
import '../constants/app_constants.dart';

class SpacedRepetitionService {
  static SpacedRepetitionService? _instance;
  static SpacedRepetitionService get instance => _instance ??= SpacedRepetitionService._();
  
  SpacedRepetitionService._();

  // Calculate next review date based on SM-2 algorithm
  DateTime calculateNextReviewDate({
    required FlashcardModel card,
    required CardDifficulty difficulty,
  }) {
    final now = DateTime.now();
    int newInterval = card.intervalDays;
    double newEaseFactor = card.easeFactor;

    switch (difficulty) {
      case CardDifficulty.again:
        // Reset to beginning
        newInterval = 1;
        newEaseFactor = (card.easeFactor - 0.2).clamp(1.3, 2.5);
        break;
        
      case CardDifficulty.hard:
        // Increase interval slightly
        newInterval = (card.intervalDays * 1.2).round().clamp(1, 365);
        newEaseFactor = (card.easeFactor - 0.15).clamp(1.3, 2.5);
        break;
        
      case CardDifficulty.medium:
        // Standard progression
        if (card.isLearning) {
          newInterval = _getNextLearningInterval(card.intervalDays);
        } else {
          newInterval = (card.intervalDays * card.easeFactor).round().clamp(1, 365);
        }
        break;
        
      case CardDifficulty.easy:
        // Accelerated progression
        if (card.isLearning) {
          newInterval = 4; // Graduate from learning
        } else {
          newInterval = (card.intervalDays * card.easeFactor * 1.3).round().clamp(1, 365);
        }
        newEaseFactor = (card.easeFactor + 0.15).clamp(1.3, 2.5);
        break;
    }

    return now.add(Duration(days: newInterval));
  }

  // Get next interval for learning cards
  int _getNextLearningInterval(int currentInterval) {
    final intervals = AppConstants.spacedRepetitionIntervals;
    final currentIndex = intervals.indexOf(currentInterval);
    
    if (currentIndex == -1 || currentIndex >= intervals.length - 1) {
      return intervals.last;
    }
    
    return intervals[currentIndex + 1];
  }

  // Update card based on review performance
  FlashcardModel updateCardAfterReview({
    required FlashcardModel card,
    required CardDifficulty difficulty,
  }) {
    final now = DateTime.now();
    final nextReviewDate = calculateNextReviewDate(card: card, difficulty: difficulty);
    
    // Calculate new ease factor
    double newEaseFactor = card.easeFactor;
    switch (difficulty) {
      case CardDifficulty.again:
        newEaseFactor = (card.easeFactor - 0.2).clamp(1.3, 2.5);
        break;
      case CardDifficulty.hard:
        newEaseFactor = (card.easeFactor - 0.15).clamp(1.3, 2.5);
        break;
      case CardDifficulty.medium:
        // No change to ease factor
        break;
      case CardDifficulty.easy:
        newEaseFactor = (card.easeFactor + 0.15).clamp(1.3, 2.5);
        break;
    }

    // Calculate new interval
    int newInterval = nextReviewDate.difference(now).inDays;
    
    // Determine if card is still learning
    bool isLearning = card.isLearning;
    if (difficulty == CardDifficulty.easy || newInterval >= 7) {
      isLearning = false;
    } else if (difficulty == CardDifficulty.again) {
      isLearning = true;
    }

    // Update review statistics
    final newReviewCount = card.reviewCount + 1;
    final newCorrectCount = difficulty != CardDifficulty.again 
        ? card.correctCount + 1 
        : card.correctCount;

    return card.copyWith(
      reviewCount: newReviewCount,
      correctCount: newCorrectCount,
      easeFactor: newEaseFactor,
      intervalDays: newInterval,
      lastDifficulty: difficulty,
      isLearning: isLearning,
      nextReviewDate: nextReviewDate,
      updatedAt: now,
    );
  }

  // Get cards due for review
  List<FlashcardModel> getCardsForReview(List<FlashcardModel> cards) {
    final now = DateTime.now();
    return cards.where((card) {
      if (card.nextReviewDate == null) return true; // New cards
      return card.nextReviewDate!.isBefore(now) || card.nextReviewDate!.isAtSameMomentAs(now);
    }).toList();
  }

  // Get new cards (never reviewed)
  List<FlashcardModel> getNewCards(List<FlashcardModel> cards) {
    return cards.where((card) => card.reviewCount == 0).toList();
  }

  // Get learning cards (short intervals)
  List<FlashcardModel> getLearningCards(List<FlashcardModel> cards) {
    return cards.where((card) => card.isLearning && card.reviewCount > 0).toList();
  }

  // Get mature cards (long intervals)
  List<FlashcardModel> getMatureCards(List<FlashcardModel> cards) {
    return cards.where((card) => !card.isLearning && card.intervalDays >= 21).toList();
  }

  // Calculate retention rate for a set of cards
  double calculateRetentionRate(List<FlashcardModel> cards) {
    if (cards.isEmpty) return 0.0;
    
    final reviewedCards = cards.where((card) => card.reviewCount > 0);
    if (reviewedCards.isEmpty) return 0.0;
    
    final totalReviews = reviewedCards.fold<int>(0, (sum, card) => sum + card.reviewCount);
    final totalCorrect = reviewedCards.fold<int>(0, (sum, card) => sum + card.correctCount);
    
    return totalCorrect / totalReviews;
  }

  // Get study schedule for cards
  Map<DateTime, List<FlashcardModel>> getStudySchedule(List<FlashcardModel> cards) {
    final schedule = <DateTime, List<FlashcardModel>>{};
    
    for (final card in cards) {
      if (card.nextReviewDate != null) {
        final date = DateTime(
          card.nextReviewDate!.year,
          card.nextReviewDate!.month,
          card.nextReviewDate!.day,
        );
        
        schedule.putIfAbsent(date, () => []).add(card);
      }
    }
    
    return schedule;
  }

  // Optimize review session order
  List<FlashcardModel> optimizeReviewOrder(List<FlashcardModel> cards) {
    // Sort cards by priority:
    // 1. Overdue cards (oldest first)
    // 2. New cards
    // 3. Learning cards
    // 4. Review cards
    
    final now = DateTime.now();
    
    cards.sort((a, b) {
      // Calculate how overdue each card is
      final aOverdue = a.nextReviewDate != null 
          ? now.difference(a.nextReviewDate!).inHours
          : 0;
      final bOverdue = b.nextReviewDate != null 
          ? now.difference(b.nextReviewDate!).inHours
          : 0;
      
      // Prioritize more overdue cards
      if (aOverdue != bOverdue) {
        return bOverdue.compareTo(aOverdue);
      }
      
      // Then prioritize by card type
      final aPriority = _getCardPriority(a);
      final bPriority = _getCardPriority(b);
      
      if (aPriority != bPriority) {
        return aPriority.compareTo(bPriority);
      }
      
      // Finally, sort by creation date (older first)
      return a.createdAt.compareTo(b.createdAt);
    });
    
    return cards;
  }

  // Get card priority for sorting
  int _getCardPriority(FlashcardModel card) {
    if (card.reviewCount == 0) return 2; // New cards
    if (card.isLearning) return 1; // Learning cards (highest priority)
    return 3; // Review cards
  }

  // Calculate optimal daily review limit
  int calculateOptimalDailyLimit({
    required int totalCards,
    required int availableMinutes,
    required double averageTimePerCard,
  }) {
    final maxCardsByTime = (availableMinutes / averageTimePerCard).floor();
    final recommendedLimit = (totalCards * 0.1).ceil(); // 10% of total cards
    
    return [maxCardsByTime, recommendedLimit, 50].reduce((a, b) => a < b ? a : b);
  }

  // Get performance statistics
  Map<String, dynamic> getPerformanceStats(List<FlashcardModel> cards) {
    final reviewedCards = cards.where((card) => card.reviewCount > 0).toList();
    
    if (reviewedCards.isEmpty) {
      return {
        'totalCards': cards.length,
        'reviewedCards': 0,
        'retentionRate': 0.0,
        'averageInterval': 0.0,
        'matureCards': 0,
        'learningCards': 0,
        'newCards': cards.length,
      };
    }
    
    final retentionRate = calculateRetentionRate(reviewedCards);
    final averageInterval = reviewedCards.fold<double>(
      0.0, 
      (sum, card) => sum + card.intervalDays,
    ) / reviewedCards.length;
    
    return {
      'totalCards': cards.length,
      'reviewedCards': reviewedCards.length,
      'retentionRate': retentionRate,
      'averageInterval': averageInterval,
      'matureCards': getMatureCards(cards).length,
      'learningCards': getLearningCards(cards).length,
      'newCards': getNewCards(cards).length,
    };
  }
}
