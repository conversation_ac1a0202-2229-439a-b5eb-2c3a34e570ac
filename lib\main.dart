import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'services/supabase_service.dart';
import 'constants/app_theme.dart';
import 'constants/app_constants.dart';
import 'views/splash_screen.dart';
import 'views/auth/auth_screen.dart';
import 'views/home/<USER>';
import 'controllers/auth_controller.dart';
import 'controllers/user_controller.dart';
import 'controllers/study_plan_controller.dart';
import 'controllers/flashcard_controller.dart';
import 'services/notification_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive
  await Hive.initFlutter();

  // Initialize Supabase
  await SupabaseService.initialize();

  // Initialize Notifications
  await NotificationService.instance.initialize();

  runApp(const StudyForgeApp());
}

class StudyForgeApp extends StatelessWidget {
  const StudyForgeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthController()),
        ChangeNotifierProvider(create: (_) => UserController()),
        ChangeNotifierProvider(create: (_) => StudyPlanController()),
        ChangeNotifierProvider(create: (_) => FlashcardController()),
      ],
      child: MaterialApp(
        title: AppConstants.appName,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        debugShowCheckedModeBanner: false,
        initialRoute: AppConstants.splashRoute,
        routes: {
          AppConstants.splashRoute: (context) => const SplashScreen(),
          AppConstants.authRoute: (context) => const AuthScreen(),
          AppConstants.homeRoute: (context) => const MainNavigation(),
        },
      ),
    );
  }
}
