import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/env_config.dart';
import '../models/study_plan_model.dart';
import '../models/flashcard_model.dart';

class GeminiService {
  static GeminiService? _instance;
  static GeminiService get instance => _instance ??= GeminiService._();

  GeminiService._();

  static const String _baseUrl =
      'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';

  Future<Map<String, dynamic>> _makeRequest(String prompt) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl?key=${EnvConfig.geminiApiKey}'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'contents': [
            {
              'parts': [
                {'text': prompt},
              ],
            },
          ],
          'generationConfig': {
            'temperature': 0.7,
            'topK': 40,
            'topP': 0.95,
            'maxOutputTokens': 2048,
          },
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data;
      } else {
        throw Exception('Failed to generate content: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error calling Gemini API: $e');
    }
  }

  String _extractTextFromResponse(Map<String, dynamic> response) {
    try {
      return response['candidates'][0]['content']['parts'][0]['text'];
    } catch (e) {
      throw Exception('Failed to extract text from Gemini response');
    }
  }

  // Syllabus Analysis and Breakdown
  Future<SyllabusAnalysis> analyzeSyllabus(String syllabusContent) async {
    final prompt = '''
Analyze the following syllabus content and extract structured information. Return a JSON response with the following structure:

{
  "course_title": "string",
  "subject": "string", 
  "topics": [
    {
      "title": "string",
      "description": "string",
      "estimated_hours": number,
      "priority": "low|medium|high|critical",
      "subtopics": ["string"]
    }
  ],
  "objectives": ["string"],
  "exam_date": "YYYY-MM-DD or null",
  "total_estimated_hours": number
}

Syllabus Content:
$syllabusContent

Please analyze this content and provide a structured breakdown focusing on learning objectives, topics, and time estimates.
''';

    try {
      final response = await _makeRequest(prompt);
      final text = _extractTextFromResponse(response);

      // Extract JSON from the response (remove any markdown formatting)
      final jsonStart = text.indexOf('{');
      final jsonEnd = text.lastIndexOf('}') + 1;
      final jsonString = text.substring(jsonStart, jsonEnd);

      final data = jsonDecode(jsonString);
      return SyllabusAnalysis.fromJson(data);
    } catch (e) {
      throw Exception('Failed to analyze syllabus: $e');
    }
  }

  // Study Plan Generation
  Future<List<StudyTopicModel>> generateStudyPlan({
    required SyllabusAnalysis analysis,
    required DateTime startDate,
    required DateTime endDate,
    required String studyPace, // 'slow', 'medium', 'fast'
    required int dailyStudyHours,
  }) async {
    final totalDays = endDate.difference(startDate).inDays;

    final prompt = '''
Create a detailed study plan based on the following information:

Course: ${analysis.courseTitle}
Subject: ${analysis.subject}
Study Period: $totalDays days (from ${startDate.toIso8601String().split('T')[0]} to ${endDate.toIso8601String().split('T')[0]})
Daily Study Hours: $dailyStudyHours
Study Pace: $studyPace
Total Estimated Hours: ${analysis.totalEstimatedHours}

Topics to cover:
${analysis.topics.map((t) => '- ${t.title} (${t.estimatedHours}h, Priority: ${t.priority})').join('\n')}

Generate a JSON array of study sessions with the following structure:
[
  {
    "title": "string",
    "description": "string", 
    "order_index": number,
    "estimated_hours": number,
    "priority": "low|medium|high|critical",
    "scheduled_date": "YYYY-MM-DD",
    "subtopics": ["string"]
  }
]

Distribute the topics across the available time period, considering:
1. Topic priorities (high priority topics should be scheduled earlier)
2. Study pace preference
3. Logical learning progression
4. Adequate time for review before exam date
5. Balance workload across days

Ensure the plan is realistic and achievable within the given timeframe.
''';

    try {
      final response = await _makeRequest(prompt);
      final text = _extractTextFromResponse(response);

      // Extract JSON from the response
      final jsonStart = text.indexOf('[');
      final jsonEnd = text.lastIndexOf(']') + 1;
      final jsonString = text.substring(jsonStart, jsonEnd);

      final List<dynamic> data = jsonDecode(jsonString);

      return data.map((item) {
        return StudyTopicModel(
          id:
              DateTime.now().millisecondsSinceEpoch.toString() +
              data.indexOf(item).toString(),
          studyPlanId: '', // Will be set when creating the study plan
          title: item['title'],
          description: item['description'],
          orderIndex: item['order_index'],
          estimatedHours: item['estimated_hours'],
          priority: TopicPriority.values.firstWhere(
            (p) => p.name == item['priority'],
            orElse: () => TopicPriority.medium,
          ),
          scheduledDate: DateTime.parse(item['scheduled_date']),
          subtopics: List<String>.from(item['subtopics'] ?? []),
        );
      }).toList();
    } catch (e) {
      throw Exception('Failed to generate study plan: $e');
    }
  }

  // Flashcard Generation
  Future<List<FlashcardModel>> generateFlashcards({
    required String content,
    required String deckId,
    int maxCards = 20,
  }) async {
    final prompt = '''
Generate flashcards from the following content. Create a JSON array with the following structure:

[
  {
    "front": "Question or term",
    "back": "Answer or definition", 
    "hint": "Optional hint (can be null)",
    "tags": ["tag1", "tag2"]
  }
]

Content:
$content

Guidelines:
1. Create maximum $maxCards flashcards
2. Focus on key concepts, definitions, and important facts
3. Make questions clear and concise
4. Provide comprehensive but concise answers
5. Add relevant tags for categorization
6. Include hints only when helpful for complex concepts
7. Vary question types (definitions, explanations, examples, applications)

Generate high-quality flashcards that will help with effective learning and retention.
''';

    try {
      final response = await _makeRequest(prompt);
      final text = _extractTextFromResponse(response);

      // Extract JSON from the response
      final jsonStart = text.indexOf('[');
      final jsonEnd = text.lastIndexOf(']') + 1;
      final jsonString = text.substring(jsonStart, jsonEnd);

      final List<dynamic> data = jsonDecode(jsonString);

      return data.map((item) {
        return FlashcardModel(
          id:
              DateTime.now().millisecondsSinceEpoch.toString() +
              data.indexOf(item).toString(),
          deckId: deckId,
          front: item['front'],
          back: item['back'],
          hint: item['hint'],
          tags: item['tags'] != null ? List<String>.from(item['tags']) : null,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      }).toList();
    } catch (e) {
      throw Exception('Failed to generate flashcards: $e');
    }
  }

  // Topic Summarization
  Future<String> summarizeTopic(String topicContent) async {
    final prompt = '''
Summarize the following topic content in a clear, concise manner. Focus on:
1. Key concepts and main ideas
2. Important definitions
3. Critical points to remember
4. Practical applications

Content:
$topicContent

Provide a well-structured summary that captures the essential information for study purposes.
''';

    try {
      final response = await _makeRequest(prompt);
      return _extractTextFromResponse(response);
    } catch (e) {
      throw Exception('Failed to summarize topic: $e');
    }
  }

  // Quiz Generation
  Future<List<QuizQuestion>> generateQuiz({
    required String content,
    int questionCount = 10,
    String questionType = 'mixed', // 'mcq', 'short_answer', 'mixed'
  }) async {
    final prompt = '''
Generate a quiz with $questionCount questions from the following content. Return a JSON array with this structure:

[
  {
    "question": "string",
    "type": "mcq|short_answer",
    "options": ["option1", "option2", "option3", "option4"], // only for MCQ
    "correct_answer": "string",
    "explanation": "string"
  }
]

Content:
$content

Question Type: $questionType
- If "mcq": Generate only multiple choice questions
- If "short_answer": Generate only short answer questions  
- If "mixed": Mix both types

Make questions challenging but fair, testing understanding rather than memorization.
''';

    try {
      final response = await _makeRequest(prompt);
      final text = _extractTextFromResponse(response);

      // Extract JSON from the response
      final jsonStart = text.indexOf('[');
      final jsonEnd = text.lastIndexOf(']') + 1;
      final jsonString = text.substring(jsonStart, jsonEnd);

      final List<dynamic> data = jsonDecode(jsonString);

      return data.map((item) => QuizQuestion.fromJson(item)).toList();
    } catch (e) {
      throw Exception('Failed to generate quiz: $e');
    }
  }

  // Explanation Generator
  Future<String> explainConcept({
    required String concept,
    String level = 'beginner', // 'beginner', 'intermediate', 'advanced'
  }) async {
    final prompt = '''
Explain the following concept in a clear, easy-to-understand way suitable for a $level level learner:

Concept: $concept

Provide:
1. A simple definition
2. Key characteristics or components
3. Real-world examples or analogies
4. Why it's important to understand
5. Common misconceptions to avoid

Make the explanation engaging and memorable.
''';

    try {
      final response = await _makeRequest(prompt);
      return _extractTextFromResponse(response);
    } catch (e) {
      throw Exception('Failed to explain concept: $e');
    }
  }
}

// Helper Classes
class SyllabusAnalysis {
  final String courseTitle;
  final String subject;
  final List<TopicAnalysis> topics;
  final List<String> objectives;
  final DateTime? examDate;
  final int totalEstimatedHours;

  SyllabusAnalysis({
    required this.courseTitle,
    required this.subject,
    required this.topics,
    required this.objectives,
    this.examDate,
    required this.totalEstimatedHours,
  });

  factory SyllabusAnalysis.fromJson(Map<String, dynamic> json) {
    return SyllabusAnalysis(
      courseTitle: json['course_title'] ?? '',
      subject: json['subject'] ?? '',
      topics:
          (json['topics'] as List<dynamic>?)
              ?.map((e) => TopicAnalysis.fromJson(e))
              .toList() ??
          [],
      objectives: List<String>.from(json['objectives'] ?? []),
      examDate:
          json['exam_date'] != null ? DateTime.parse(json['exam_date']) : null,
      totalEstimatedHours: json['total_estimated_hours'] ?? 0,
    );
  }
}

class TopicAnalysis {
  final String title;
  final String description;
  final int estimatedHours;
  final String priority;
  final List<String> subtopics;

  TopicAnalysis({
    required this.title,
    required this.description,
    required this.estimatedHours,
    required this.priority,
    required this.subtopics,
  });

  factory TopicAnalysis.fromJson(Map<String, dynamic> json) {
    return TopicAnalysis(
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      estimatedHours: json['estimated_hours'] ?? 1,
      priority: json['priority'] ?? 'medium',
      subtopics: List<String>.from(json['subtopics'] ?? []),
    );
  }
}

class QuizQuestion {
  final String question;
  final String type;
  final List<String>? options;
  final String correctAnswer;
  final String explanation;

  QuizQuestion({
    required this.question,
    required this.type,
    this.options,
    required this.correctAnswer,
    required this.explanation,
  });

  factory QuizQuestion.fromJson(Map<String, dynamic> json) {
    return QuizQuestion(
      question: json['question'] ?? '',
      type: json['type'] ?? 'short_answer',
      options:
          json['options'] != null ? List<String>.from(json['options']) : null,
      correctAnswer: json['correct_answer'] ?? '',
      explanation: json['explanation'] ?? '',
    );
  }
}
