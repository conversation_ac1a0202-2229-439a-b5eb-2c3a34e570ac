import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

class ProgressChart extends StatelessWidget {
  final List<ProgressData> data;
  final String title;
  final Color primaryColor;
  final double height;

  const ProgressChart({
    super.key,
    required this.data,
    required this.title,
    this.primaryColor = AppColors.primary,
    this.height = 200,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppTextStyles.headline3,
            ),
            const SizedBox(height: AppDimensions.paddingL),
            SizedBox(
              height: height,
              child: _buildChart(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChart() {
    if (data.isEmpty) {
      return Center(
        child: Text(
          'No data available',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      );
    }

    final maxValue = data.map((e) => e.value).reduce((a, b) => a > b ? a : b);
    
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: data.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;
        final barHeight = maxValue > 0 ? (item.value / maxValue) * (height - 40) : 0.0;
        
        return Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 2),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Value label
                Text(
                  item.value.toStringAsFixed(0),
                  style: AppTextStyles.bodySmall.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                // Bar
                Container(
                  height: barHeight,
                  decoration: BoxDecoration(
                    color: primaryColor.withOpacity(0.8),
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(4),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                // Label
                Text(
                  item.label,
                  style: AppTextStyles.bodySmall,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }
}

class CircularProgressChart extends StatelessWidget {
  final double progress;
  final String label;
  final String value;
  final Color color;
  final double size;

  const CircularProgressChart({
    super.key,
    required this.progress,
    required this.label,
    required this.value,
    this.color = AppColors.primary,
    this.size = 120,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          width: size,
          height: size,
          child: Stack(
            children: [
              // Background circle
              SizedBox(
                width: size,
                height: size,
                child: CircularProgressIndicator(
                  value: 1.0,
                  strokeWidth: 8,
                  backgroundColor: AppColors.surfaceVariant,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppColors.surfaceVariant,
                  ),
                ),
              ),
              // Progress circle
              SizedBox(
                width: size,
                height: size,
                child: CircularProgressIndicator(
                  value: progress,
                  strokeWidth: 8,
                  backgroundColor: Colors.transparent,
                  valueColor: AlwaysStoppedAnimation<Color>(color),
                ),
              ),
              // Center content
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      value,
                      style: AppTextStyles.headline2.copyWith(
                        color: color,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${(progress * 100).toInt()}%',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: AppDimensions.paddingS),
        Text(
          label,
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

class StudyStreakWidget extends StatelessWidget {
  final int currentStreak;
  final int longestStreak;
  final DateTime? lastStudyDate;

  const StudyStreakWidget({
    super.key,
    required this.currentStreak,
    required this.longestStreak,
    this.lastStudyDate,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  Icons.local_fire_department,
                  color: AppColors.accent,
                  size: AppDimensions.iconL,
                ),
                const SizedBox(width: AppDimensions.paddingS),
                Text(
                  'Study Streak',
                  style: AppTextStyles.headline3,
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingL),
            Row(
              children: [
                Expanded(
                  child: _buildStreakItem(
                    'Current',
                    currentStreak.toString(),
                    AppColors.accent,
                  ),
                ),
                Container(
                  width: 1,
                  height: 60,
                  color: AppColors.surfaceVariant,
                ),
                Expanded(
                  child: _buildStreakItem(
                    'Best',
                    longestStreak.toString(),
                    AppColors.textSecondary,
                  ),
                ),
              ],
            ),
            if (lastStudyDate != null) ...[
              const SizedBox(height: AppDimensions.paddingM),
              Text(
                'Last study: ${_formatLastStudyDate(lastStudyDate!)}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStreakItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: AppTextStyles.headline1.copyWith(
            color: color,
            fontSize: 36,
          ),
        ),
        Text(
          label,
          style: AppTextStyles.bodyMedium,
        ),
      ],
    );
  }

  String _formatLastStudyDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;
    
    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday';
    } else {
      return '$difference days ago';
    }
  }
}

class AchievementBadge extends StatelessWidget {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final bool isUnlocked;

  const AchievementBadge({
    super.key,
    required this.title,
    required this.description,
    required this.icon,
    this.color = AppColors.accent,
    this.isUnlocked = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: isUnlocked 
            ? color.withOpacity(0.1) 
            : AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: isUnlocked 
              ? color.withOpacity(0.3) 
              : AppColors.textTertiary.withOpacity(0.3),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: isUnlocked ? color : AppColors.textTertiary,
            size: AppDimensions.iconL,
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            title,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: isUnlocked ? AppColors.textPrimary : AppColors.textTertiary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.paddingXS),
          Text(
            description,
            style: AppTextStyles.bodySmall.copyWith(
              color: isUnlocked ? AppColors.textSecondary : AppColors.textTertiary,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

class ProgressData {
  final String label;
  final double value;

  ProgressData({
    required this.label,
    required this.value,
  });
}
