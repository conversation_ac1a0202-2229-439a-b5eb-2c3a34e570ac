import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/env_config.dart';
import '../models/user_model.dart';

class SupabaseService {
  static SupabaseService? _instance;
  static SupabaseService get instance => _instance ??= SupabaseService._();

  SupabaseService._();

  SupabaseClient get client => Supabase.instance.client;
  User? get currentUser => client.auth.currentUser;
  bool get isAuthenticated => currentUser != null;

  // Initialize Supabase
  static Future<void> initialize() async {
    // Validate configuration before initializing
    EnvConfig.validateConfig();

    await Supabase.initialize(
      url: EnvConfig.supabaseUrl,
      anonKey: EnvConfig.supabaseAnonKey,
    );
  }

  // Authentication Methods
  Future<AuthResponse> signUp({
    required String email,
    required String password,
    String? fullName,
  }) async {
    try {
      final response = await client.auth.signUp(
        email: email,
        password: password,
        data: fullName != null ? {'full_name': fullName} : null,
      );

      if (response.user != null) {
        await _createUserProfile(response.user!, fullName);
      }

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    try {
      final response = await client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        await _updateLastLogin(response.user!.id);
      }

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> signOut() async {
    try {
      await client.auth.signOut();
    } catch (e) {
      rethrow;
    }
  }

  Future<void> resetPassword(String email) async {
    try {
      await client.auth.resetPasswordForEmail(email);
    } catch (e) {
      rethrow;
    }
  }

  // User Profile Methods
  Future<void> _createUserProfile(User user, String? fullName) async {
    try {
      final userModel = UserModel(
        id: user.id,
        email: user.email!,
        fullName: fullName ?? user.userMetadata?['full_name'],
        avatarUrl: user.userMetadata?['avatar_url'],
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        preferences: UserPreferences(),
        stats: UserStats(),
      );

      await client.from('users').insert(userModel.toJson());
    } catch (e) {
      rethrow;
    }
  }

  Future<UserModel?> getUserProfile(String userId) async {
    try {
      final response =
          await client.from('users').select().eq('id', userId).single();

      return UserModel.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  Future<void> updateUserProfile(UserModel user) async {
    try {
      await client.from('users').update(user.toJson()).eq('id', user.id);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> _updateLastLogin(String userId) async {
    try {
      await client
          .from('users')
          .update({'last_login_at': DateTime.now().toIso8601String()})
          .eq('id', userId);
    } catch (e) {
      // Don't throw error for last login update failure
    }
  }

  // File Upload Methods
  Future<String> uploadFile({
    required String bucket,
    required String path,
    required List<int> bytes,
    String? contentType,
  }) async {
    try {
      await client.storage
          .from(bucket)
          .uploadBinary(
            path,
            bytes,
            fileOptions: FileOptions(contentType: contentType, upsert: true),
          );

      return client.storage.from(bucket).getPublicUrl(path);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> deleteFile({
    required String bucket,
    required String path,
  }) async {
    try {
      await client.storage.from(bucket).remove([path]);
    } catch (e) {
      rethrow;
    }
  }

  // Database CRUD Operations
  Future<List<Map<String, dynamic>>> select({
    required String table,
    String columns = '*',
    Map<String, dynamic>? filters,
    String? orderBy,
    bool ascending = true,
    int? limit,
  }) async {
    try {
      var query = client.from(table).select(columns);

      if (filters != null) {
        filters.forEach((key, value) {
          query = query.eq(key, value);
        });
      }

      if (orderBy != null) {
        query = query.order(orderBy, ascending: ascending);
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      return await query;
    } catch (e) {
      rethrow;
    }
  }

  Future<Map<String, dynamic>> insert({
    required String table,
    required Map<String, dynamic> data,
  }) async {
    try {
      final response = await client.from(table).insert(data).select().single();

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Map<String, dynamic>> update({
    required String table,
    required Map<String, dynamic> data,
    required Map<String, dynamic> filters,
  }) async {
    try {
      var query = client.from(table).update(data);

      filters.forEach((key, value) {
        query = query.eq(key, value);
      });

      final response = await query.select().single();
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> delete({
    required String table,
    required Map<String, dynamic> filters,
  }) async {
    try {
      var query = client.from(table).delete();

      filters.forEach((key, value) {
        query = query.eq(key, value);
      });

      await query;
    } catch (e) {
      rethrow;
    }
  }

  // Real-time subscriptions
  RealtimeChannel subscribeToTable({
    required String table,
    required void Function(PostgresChangePayload) onData,
    PostgresChangeEvent event = PostgresChangeEvent.all,
    Map<String, dynamic>? filters,
  }) {
    var channel = client.channel('public:$table');

    var subscription = channel.onPostgresChanges(
      event: event,
      schema: 'public',
      table: table,
      callback: onData,
    );

    if (filters != null) {
      filters.forEach((key, value) {
        subscription = subscription.filter(key, 'eq', value);
      });
    }

    channel.subscribe();
    return channel;
  }

  void unsubscribe(RealtimeChannel channel) {
    channel.unsubscribe();
  }
}
