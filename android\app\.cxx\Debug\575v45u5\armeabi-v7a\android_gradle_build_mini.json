{"buildFiles": ["C:\\Users\\<USER>\\Desktop\\5most\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\3app\\tutor_me\\android\\app\\.cxx\\Debug\\575v45u5\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\3app\\tutor_me\\android\\app\\.cxx\\Debug\\575v45u5\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}