import 'package:flutter/material.dart';
import '../models/study_plan_model.dart';
import '../services/supabase_service.dart';
import '../services/gemini_service.dart';
import '../services/file_service.dart';

enum StudyPlanState {
  initial,
  loading,
  loaded,
  creating,
  error,
}

class StudyPlanController extends ChangeNotifier {
  final SupabaseService _supabaseService = SupabaseService.instance;
  final GeminiService _geminiService = GeminiService.instance;
  final FileService _fileService = FileService.instance;

  StudyPlanState _state = StudyPlanState.initial;
  List<StudyPlanModel> _studyPlans = [];
  StudyPlanModel? _currentStudyPlan;
  String? _errorMessage;

  // Getters
  StudyPlanState get state => _state;
  List<StudyPlanModel> get studyPlans => _studyPlans;
  StudyPlanModel? get currentStudyPlan => _currentStudyPlan;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _state == StudyPlanState.loading || _state == StudyPlanState.creating;

  // Load all study plans for user
  Future<void> loadStudyPlans(String userId) async {
    try {
      _setState(StudyPlanState.loading);
      
      final data = await _supabaseService.select(
        table: 'study_plans',
        filters: {'user_id': userId},
        orderBy: 'created_at',
        ascending: false,
      );

      _studyPlans = data.map((item) => StudyPlanModel.fromJson(item)).toList();
      _setState(StudyPlanState.loaded);
    } catch (e) {
      _setError('Failed to load study plans: $e');
    }
  }

  // Create study plan from uploaded syllabus
  Future<StudyPlanModel?> createStudyPlanFromSyllabus({
    required String userId,
    required FilePickResult syllabusFile,
    required String title,
    required String subject,
    required DateTime startDate,
    required DateTime endDate,
    DateTime? examDate,
    required String studyPace,
    required int dailyStudyHours,
  }) async {
    try {
      _setState(StudyPlanState.creating);

      // Extract text from syllabus file
      final syllabusContent = await _fileService.extractTextFromFile(syllabusFile);

      // Analyze syllabus with AI
      final analysis = await _geminiService.analyzeSyllabus(syllabusContent);

      // Generate study plan with AI
      final topics = await _geminiService.generateStudyPlan(
        analysis: analysis,
        startDate: startDate,
        endDate: endDate,
        studyPace: studyPace,
        dailyStudyHours: dailyStudyHours,
      );

      // Create study plan model
      final studyPlan = StudyPlanModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        title: title.isNotEmpty ? title : analysis.courseTitle,
        description: 'Generated from syllabus analysis',
        subject: subject.isNotEmpty ? subject : analysis.subject,
        startDate: startDate,
        endDate: endDate,
        examDate: examDate ?? analysis.examDate,
        topics: topics.map((topic) => topic.copyWith(studyPlanId: DateTime.now().millisecondsSinceEpoch.toString())).toList(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        syllabusContent: syllabusContent,
      );

      // Save to database
      final savedData = await _supabaseService.insert(
        table: 'study_plans',
        data: studyPlan.toJson(),
      );

      final savedStudyPlan = StudyPlanModel.fromJson(savedData);
      
      // Add to local list
      _studyPlans.insert(0, savedStudyPlan);
      _currentStudyPlan = savedStudyPlan;
      
      _setState(StudyPlanState.loaded);
      return savedStudyPlan;
    } catch (e) {
      _setError('Failed to create study plan: $e');
      return null;
    }
  }

  // Create manual study plan
  Future<StudyPlanModel?> createManualStudyPlan({
    required String userId,
    required String title,
    required String subject,
    String? description,
    required DateTime startDate,
    required DateTime endDate,
    DateTime? examDate,
    List<StudyTopicModel>? topics,
  }) async {
    try {
      _setState(StudyPlanState.creating);

      final studyPlan = StudyPlanModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        title: title,
        description: description,
        subject: subject,
        startDate: startDate,
        endDate: endDate,
        examDate: examDate,
        topics: topics ?? [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save to database
      final savedData = await _supabaseService.insert(
        table: 'study_plans',
        data: studyPlan.toJson(),
      );

      final savedStudyPlan = StudyPlanModel.fromJson(savedData);
      
      // Add to local list
      _studyPlans.insert(0, savedStudyPlan);
      _currentStudyPlan = savedStudyPlan;
      
      _setState(StudyPlanState.loaded);
      return savedStudyPlan;
    } catch (e) {
      _setError('Failed to create study plan: $e');
      return null;
    }
  }

  // Update study plan
  Future<bool> updateStudyPlan(StudyPlanModel studyPlan) async {
    try {
      _setState(StudyPlanState.loading);

      final updatedPlan = studyPlan.copyWith(updatedAt: DateTime.now());

      await _supabaseService.update(
        table: 'study_plans',
        data: updatedPlan.toJson(),
        filters: {'id': studyPlan.id},
      );

      // Update local list
      final index = _studyPlans.indexWhere((plan) => plan.id == studyPlan.id);
      if (index != -1) {
        _studyPlans[index] = updatedPlan;
      }

      if (_currentStudyPlan?.id == studyPlan.id) {
        _currentStudyPlan = updatedPlan;
      }

      _setState(StudyPlanState.loaded);
      return true;
    } catch (e) {
      _setError('Failed to update study plan: $e');
      return false;
    }
  }

  // Delete study plan
  Future<bool> deleteStudyPlan(String studyPlanId) async {
    try {
      _setState(StudyPlanState.loading);

      await _supabaseService.delete(
        table: 'study_plans',
        filters: {'id': studyPlanId},
      );

      // Remove from local list
      _studyPlans.removeWhere((plan) => plan.id == studyPlanId);

      if (_currentStudyPlan?.id == studyPlanId) {
        _currentStudyPlan = null;
      }

      _setState(StudyPlanState.loaded);
      return true;
    } catch (e) {
      _setError('Failed to delete study plan: $e');
      return false;
    }
  }

  // Update topic status
  Future<bool> updateTopicStatus(String studyPlanId, String topicId, TopicStatus status) async {
    try {
      final studyPlan = _studyPlans.firstWhere((plan) => plan.id == studyPlanId);
      final topics = List<StudyTopicModel>.from(studyPlan.topics);
      
      final topicIndex = topics.indexWhere((topic) => topic.id == topicId);
      if (topicIndex == -1) return false;

      topics[topicIndex] = topics[topicIndex].copyWith(
        status: status,
        completedDate: status == TopicStatus.completed ? DateTime.now() : null,
      );

      // Calculate progress
      final completedTopics = topics.where((t) => t.status == TopicStatus.completed).length;
      final progress = topics.isNotEmpty ? completedTopics / topics.length : 0.0;

      final updatedPlan = studyPlan.copyWith(
        topics: topics,
        progress: progress,
        updatedAt: DateTime.now(),
      );

      return await updateStudyPlan(updatedPlan);
    } catch (e) {
      _setError('Failed to update topic status: $e');
      return false;
    }
  }

  // Add study session
  Future<bool> addStudySession({
    required String studyPlanId,
    String? topicId,
    required DateTime startTime,
    DateTime? endTime,
    int? durationMinutes,
    String? notes,
  }) async {
    try {
      final studyPlan = _studyPlans.firstWhere((plan) => plan.id == studyPlanId);
      final sessions = List<StudySessionModel>.from(studyPlan.sessions);

      final session = StudySessionModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        studyPlanId: studyPlanId,
        topicId: topicId,
        startTime: startTime,
        endTime: endTime,
        durationMinutes: durationMinutes ?? 0,
        notes: notes,
        pointsEarned: (durationMinutes ?? 0) ~/ 10, // 1 point per 10 minutes
      );

      sessions.add(session);

      final updatedPlan = studyPlan.copyWith(
        sessions: sessions,
        updatedAt: DateTime.now(),
      );

      return await updateStudyPlan(updatedPlan);
    } catch (e) {
      _setError('Failed to add study session: $e');
      return false;
    }
  }

  // Set current study plan
  void setCurrentStudyPlan(StudyPlanModel? studyPlan) {
    _currentStudyPlan = studyPlan;
    notifyListeners();
  }

  // Search study plans
  List<StudyPlanModel> searchStudyPlans(String query) {
    if (query.isEmpty) return _studyPlans;
    
    return _studyPlans.where((plan) {
      return plan.title.toLowerCase().contains(query.toLowerCase()) ||
             plan.subject.toLowerCase().contains(query.toLowerCase()) ||
             (plan.description?.toLowerCase().contains(query.toLowerCase()) ?? false);
    }).toList();
  }

  // Filter study plans by status
  List<StudyPlanModel> filterByStatus(StudyPlanStatus status) {
    return _studyPlans.where((plan) => plan.status == status).toList();
  }

  // Get study plans by subject
  List<StudyPlanModel> getStudyPlansBySubject(String subject) {
    return _studyPlans.where((plan) => plan.subject == subject).toList();
  }

  // Clear error
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void _setState(StudyPlanState newState) {
    _state = newState;
    if (newState != StudyPlanState.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _state = StudyPlanState.error;
    notifyListeners();
  }

  @override
  void dispose() {
    super.dispose();
  }
}
