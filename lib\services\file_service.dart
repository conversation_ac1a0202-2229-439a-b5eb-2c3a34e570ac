import 'dart:io';
import 'dart:typed_data';
import 'package:file_picker/file_picker.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import '../constants/app_constants.dart';
import '../config/env_config.dart';
import 'supabase_service.dart';

class FileService {
  static FileService? _instance;
  static FileService get instance => _instance ??= FileService._();
  
  FileService._();

  final SupabaseService _supabaseService = SupabaseService.instance;

  // Pick and validate file
  Future<FilePickResult?> pickFile({
    List<String>? allowedExtensions,
    int? maxSizeBytes,
  }) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: allowedExtensions ?? AppConstants.allowedFileTypes,
        allowMultiple: false,
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        
        // Validate file size
        final maxSize = maxSizeBytes ?? (AppConstants.maxFileSizeMB * 1024 * 1024);
        if (file.size > maxSize) {
          throw FileException('File size exceeds ${AppConstants.maxFileSizeMB}MB limit');
        }

        // Validate file type
        final extension = file.extension?.toLowerCase();
        final allowedExts = allowedExtensions ?? AppConstants.allowedFileTypes;
        if (extension == null || !allowedExts.contains(extension)) {
          throw FileException('File type not supported. Allowed types: ${allowedExts.join(', ')}');
        }

        return FilePickResult(
          name: file.name,
          path: file.path,
          bytes: file.bytes!,
          size: file.size,
          extension: extension,
        );
      }
      
      return null;
    } catch (e) {
      if (e is FileException) rethrow;
      throw FileException('Failed to pick file: $e');
    }
  }

  // Extract text from different file types
  Future<String> extractTextFromFile(FilePickResult file) async {
    try {
      switch (file.extension) {
        case 'pdf':
          return await _extractTextFromPdf(file.bytes);
        case 'txt':
          return String.fromCharCodes(file.bytes);
        case 'doc':
        case 'docx':
          // For now, we'll return a placeholder
          // In a real implementation, you'd use a library like docx_reader
          return _extractTextFromDocx(file.bytes);
        default:
          throw FileException('Unsupported file type for text extraction: ${file.extension}');
      }
    } catch (e) {
      if (e is FileException) rethrow;
      throw FileException('Failed to extract text from file: $e');
    }
  }

  // Extract text from PDF (simplified implementation)
  Future<String> _extractTextFromPdf(Uint8List bytes) async {
    try {
      // This is a simplified implementation
      // In a real app, you'd use a proper PDF text extraction library
      // For now, we'll return a placeholder that indicates PDF content was detected
      return '''
      PDF Content Detected
      
      This is a placeholder for PDF text extraction.
      In a production app, this would contain the actual extracted text from the PDF file.
      
      The PDF parsing functionality would extract:
      - Course title and information
      - Learning objectives
      - Topic lists and descriptions
      - Assignment dates and deadlines
      - Exam schedules
      - Reading materials and references
      
      This extracted content would then be processed by the AI service to generate
      structured study plans and learning materials.
      ''';
    } catch (e) {
      throw FileException('Failed to extract text from PDF: $e');
    }
  }

  // Extract text from DOCX (placeholder implementation)
  String _extractTextFromDocx(Uint8List bytes) {
    // This is a placeholder implementation
    // In a real app, you'd use a library like docx_reader or archive
    return '''
    DOCX Content Detected
    
    This is a placeholder for DOCX text extraction.
    In a production app, this would contain the actual extracted text from the Word document.
    
    The document parsing would extract formatted text, tables, and other content
    that can be processed by the AI service for study plan generation.
    ''';
  }

  // Upload file to Supabase storage
  Future<String> uploadFile({
    required FilePickResult file,
    required String userId,
    String? customPath,
  }) async {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = '${userId}_${timestamp}_${file.name}';
      final path = customPath ?? 'syllabi/$fileName';

      final url = await _supabaseService.uploadFile(
        bucket: EnvConfig.storageBucket,
        path: path,
        bytes: file.bytes,
        contentType: _getContentType(file.extension),
      );

      return url;
    } catch (e) {
      throw FileException('Failed to upload file: $e');
    }
  }

  // Delete file from storage
  Future<void> deleteFile(String path) async {
    try {
      await _supabaseService.deleteFile(
        bucket: EnvConfig.storageBucket,
        path: path,
      );
    } catch (e) {
      throw FileException('Failed to delete file: $e');
    }
  }

  // Get content type for file extension
  String _getContentType(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case 'txt':
        return 'text/plain';
      default:
        return 'application/octet-stream';
    }
  }

  // Validate file for syllabus processing
  bool isValidSyllabusFile(FilePickResult file) {
    // Check file size
    if (file.size > AppConstants.maxFileSizeMB * 1024 * 1024) {
      return false;
    }

    // Check file type
    if (!AppConstants.allowedFileTypes.contains(file.extension)) {
      return false;
    }

    // Additional validation can be added here
    return true;
  }

  // Get file info without uploading
  Map<String, dynamic> getFileInfo(FilePickResult file) {
    return {
      'name': file.name,
      'size': file.size,
      'sizeFormatted': _formatFileSize(file.size),
      'extension': file.extension,
      'type': _getContentType(file.extension),
      'isValid': isValidSyllabusFile(file),
    };
  }

  // Format file size for display
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}

// File pick result model
class FilePickResult {
  final String name;
  final String? path;
  final Uint8List bytes;
  final int size;
  final String extension;

  FilePickResult({
    required this.name,
    this.path,
    required this.bytes,
    required this.size,
    required this.extension,
  });

  @override
  String toString() {
    return 'FilePickResult(name: $name, size: $size, extension: $extension)';
  }
}

// Custom exception for file operations
class FileException implements Exception {
  final String message;
  
  FileException(this.message);
  
  @override
  String toString() => 'FileException: $message';
}
