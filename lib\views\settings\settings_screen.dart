import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../controllers/user_controller.dart';
import '../../services/notification_service.dart';
import '../../constants/app_constants.dart';
import '../../widgets/custom_button.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: Consumer<UserController>(
        builder: (context, userController, child) {
          final preferences = userController.preferences;
          
          return ListView(
            padding: const EdgeInsets.all(AppDimensions.paddingL),
            children: [
              // Study Preferences Section
              _buildSectionHeader('Study Preferences'),
              _buildStudyGoalSetting(userController),
              _buildStudyPaceSetting(userController),
              _buildStudyDaysSetting(userController),
              
              const SizedBox(height: AppDimensions.paddingL),
              
              // Notification Settings Section
              _buildSectionHeader('Notifications'),
              _buildNotificationToggle(userController),
              _buildReminderTimeSetting(userController),
              
              const SizedBox(height: AppDimensions.paddingL),
              
              // App Preferences Section
              _buildSectionHeader('App Preferences'),
              _buildGamificationToggle(userController),
              _buildSoundToggle(userController),
              _buildThemeSetting(userController),
              
              const SizedBox(height: AppDimensions.paddingL),
              
              // Data & Privacy Section
              _buildSectionHeader('Data & Privacy'),
              _buildDataManagementOptions(),
              
              const SizedBox(height: AppDimensions.paddingXL),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      child: Text(
        title,
        style: AppTextStyles.headline3.copyWith(
          color: AppColors.primary,
        ),
      ),
    );
  }

  Widget _buildStudyGoalSetting(UserController userController) {
    return Card(
      child: ListTile(
        leading: const Icon(Icons.timer, color: AppColors.primary),
        title: const Text('Daily Study Goal'),
        subtitle: Text('${userController.preferences.dailyStudyGoalMinutes} minutes'),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _showStudyGoalDialog(userController),
      ),
    );
  }

  Widget _buildStudyPaceSetting(UserController userController) {
    return Card(
      child: ListTile(
        leading: const Icon(Icons.speed, color: AppColors.primary),
        title: const Text('Preferred Study Pace'),
        subtitle: Text(_formatStudyPace(userController.preferences.preferredStudyPace)),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _showStudyPaceDialog(userController),
      ),
    );
  }

  Widget _buildStudyDaysSetting(UserController userController) {
    return Card(
      child: ListTile(
        leading: const Icon(Icons.calendar_today, color: AppColors.primary),
        title: const Text('Study Days'),
        subtitle: Text(_formatStudyDays(userController.preferences.studyDays)),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _showStudyDaysDialog(userController),
      ),
    );
  }

  Widget _buildNotificationToggle(UserController userController) {
    return Card(
      child: SwitchListTile(
        secondary: const Icon(Icons.notifications, color: AppColors.primary),
        title: const Text('Study Reminders'),
        subtitle: const Text('Daily study reminder notifications'),
        value: userController.preferences.notificationsEnabled,
        onChanged: (value) async {
          await userController.updateNotificationSettings(value);
          if (value) {
            await NotificationService.instance.requestPermissions();
          }
        },
      ),
    );
  }

  Widget _buildReminderTimeSetting(UserController userController) {
    return Card(
      child: ListTile(
        leading: const Icon(Icons.access_time, color: AppColors.primary),
        title: const Text('Reminder Time'),
        subtitle: Text(userController.preferences.studyReminderTime.toString()),
        trailing: const Icon(Icons.chevron_right),
        enabled: userController.preferences.notificationsEnabled,
        onTap: userController.preferences.notificationsEnabled
            ? () => _showReminderTimeDialog(userController)
            : null,
      ),
    );
  }

  Widget _buildGamificationToggle(UserController userController) {
    return Card(
      child: SwitchListTile(
        secondary: const Icon(Icons.emoji_events, color: AppColors.primary),
        title: const Text('Gamification'),
        subtitle: const Text('Enable points, achievements, and streaks'),
        value: userController.preferences.gamificationEnabled,
        onChanged: (value) {
          // TODO: Update gamification preference
        },
      ),
    );
  }

  Widget _buildSoundToggle(UserController userController) {
    return Card(
      child: SwitchListTile(
        secondary: const Icon(Icons.volume_up, color: AppColors.primary),
        title: const Text('Sound Effects'),
        subtitle: const Text('Enable app sounds and feedback'),
        value: userController.preferences.soundEnabled,
        onChanged: (value) {
          // TODO: Update sound preference
        },
      ),
    );
  }

  Widget _buildThemeSetting(UserController userController) {
    return Card(
      child: ListTile(
        leading: const Icon(Icons.palette, color: AppColors.primary),
        title: const Text('Theme'),
        subtitle: Text(_formatTheme(userController.preferences.theme)),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _showThemeDialog(userController),
      ),
    );
  }

  Widget _buildDataManagementOptions() {
    return Column(
      children: [
        Card(
          child: ListTile(
            leading: const Icon(Icons.download, color: AppColors.primary),
            title: const Text('Export Data'),
            subtitle: const Text('Download your study data'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Export feature coming soon!')),
              );
            },
          ),
        ),
        Card(
          child: ListTile(
            leading: const Icon(Icons.delete_forever, color: AppColors.error),
            title: const Text('Reset Progress'),
            subtitle: const Text('Clear all study data and progress'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () => _showResetProgressDialog(),
          ),
        ),
      ],
    );
  }

  void _showStudyGoalDialog(UserController userController) {
    int selectedMinutes = userController.preferences.dailyStudyGoalMinutes;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Daily Study Goal'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Current goal: $selectedMinutes minutes'),
            const SizedBox(height: AppDimensions.paddingM),
            Slider(
              value: selectedMinutes.toDouble(),
              min: 15,
              max: 480, // 8 hours
              divisions: 31,
              label: '$selectedMinutes min',
              onChanged: (value) {
                setState(() {
                  selectedMinutes = value.round();
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              userController.updateStudyGoal(selectedMinutes);
              Navigator.of(context).pop();
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showStudyPaceDialog(UserController userController) {
    String selectedPace = userController.preferences.preferredStudyPace;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Study Pace'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('Relaxed'),
              subtitle: const Text('Take your time, steady progress'),
              value: 'slow',
              groupValue: selectedPace,
              onChanged: (value) {
                setState(() {
                  selectedPace = value!;
                });
              },
            ),
            RadioListTile<String>(
              title: const Text('Moderate'),
              subtitle: const Text('Balanced approach'),
              value: 'medium',
              groupValue: selectedPace,
              onChanged: (value) {
                setState(() {
                  selectedPace = value!;
                });
              },
            ),
            RadioListTile<String>(
              title: const Text('Intensive'),
              subtitle: const Text('Fast-paced learning'),
              value: 'fast',
              groupValue: selectedPace,
              onChanged: (value) {
                setState(() {
                  selectedPace = value!;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Update study pace preference
              Navigator.of(context).pop();
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showStudyDaysDialog(UserController userController) {
    List<int> selectedDays = List.from(userController.preferences.studyDays);
    final dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Study Days'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(7, (index) {
              final dayNumber = index + 1;
              return CheckboxListTile(
                title: Text(dayNames[index]),
                value: selectedDays.contains(dayNumber),
                onChanged: (value) {
                  setState(() {
                    if (value == true) {
                      selectedDays.add(dayNumber);
                    } else {
                      selectedDays.remove(dayNumber);
                    }
                  });
                },
              );
            }),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: selectedDays.isNotEmpty ? () {
                userController.updateStudyDays(selectedDays);
                Navigator.of(context).pop();
              } : null,
              child: const Text('Save'),
            ),
          ],
        ),
      ),
    );
  }

  void _showReminderTimeDialog(UserController userController) {
    TimeOfDay selectedTime = userController.preferences.studyReminderTime;
    
    showTimePicker(
      context: context,
      initialTime: selectedTime,
    ).then((time) {
      if (time != null) {
        // TODO: Update reminder time preference
      }
    });
  }

  void _showThemeDialog(UserController userController) {
    String selectedTheme = userController.preferences.theme;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Theme'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('Light'),
              value: 'light',
              groupValue: selectedTheme,
              onChanged: (value) {
                setState(() {
                  selectedTheme = value!;
                });
              },
            ),
            RadioListTile<String>(
              title: const Text('Dark'),
              value: 'dark',
              groupValue: selectedTheme,
              onChanged: (value) {
                setState(() {
                  selectedTheme = value!;
                });
              },
            ),
            RadioListTile<String>(
              title: const Text('System'),
              value: 'system',
              groupValue: selectedTheme,
              onChanged: (value) {
                setState(() {
                  selectedTheme = value!;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Update theme preference
              Navigator.of(context).pop();
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showResetProgressDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Progress'),
        content: const Text(
          'This will permanently delete all your study data, progress, and achievements. This action cannot be undone.\n\nAre you sure you want to continue?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final userController = Provider.of<UserController>(context, listen: false);
              userController.resetStats();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Progress reset successfully')),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  String _formatStudyPace(String pace) {
    switch (pace) {
      case 'slow': return 'Relaxed';
      case 'medium': return 'Moderate';
      case 'fast': return 'Intensive';
      default: return 'Moderate';
    }
  }

  String _formatStudyDays(List<int> days) {
    if (days.length == 7) return 'Every day';
    if (days.length == 5 && days.every((d) => d <= 5)) return 'Weekdays';
    if (days.length == 2 && days.contains(6) && days.contains(7)) return 'Weekends';
    
    final dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return days.map((d) => dayNames[d - 1]).join(', ');
  }

  String _formatTheme(String theme) {
    switch (theme) {
      case 'light': return 'Light';
      case 'dark': return 'Dark';
      case 'system': return 'System';
      default: return 'System';
    }
  }
}
