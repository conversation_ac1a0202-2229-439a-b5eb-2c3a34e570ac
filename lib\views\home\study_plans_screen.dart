import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../widgets/custom_button.dart';

class StudyPlansScreen extends StatelessWidget {
  const StudyPlansScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Study Plans'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              _showCreateStudyPlanDialog(context);
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          children: [
            // Search and Filter
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: 'Search study plans...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: AppColors.surfaceVariant,
                    ),
                  ),
                ),
                const SizedBox(width: AppDimensions.paddingM),
                IconButton(
                  icon: const Icon(Icons.filter_list),
                  onPressed: () {
                    // TODO: Implement filter
                  },
                ),
              ],
            ),
            
            const SizedBox(height: AppDimensions.paddingL),
            
            // Study Plans List
            Expanded(
              child: ListView.builder(
                itemCount: 3, // Placeholder count
                itemBuilder: (context, index) {
                  return _buildStudyPlanCard(context, index);
                },
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showCreateStudyPlanDialog(context);
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildStudyPlanCard(BuildContext context, int index) {
    final titles = ['Mathematics Calculus', 'Physics Mechanics', 'Chemistry Organic'];
    final subjects = ['Mathematics', 'Physics', 'Chemistry'];
    final progress = [0.65, 0.30, 0.85];
    final colors = [AppColors.primary, AppColors.secondary, AppColors.accent];
    
    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: colors[index],
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                const SizedBox(width: AppDimensions.paddingS),
                Expanded(
                  child: Text(
                    titles[index],
                    style: AppTextStyles.headline3,
                  ),
                ),
                PopupMenuButton(
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Text('Edit'),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Text('Delete'),
                    ),
                  ],
                  onSelected: (value) {
                    // TODO: Handle menu actions
                  },
                ),
              ],
            ),
            
            const SizedBox(height: AppDimensions.paddingS),
            
            Text(
              subjects[index],
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            
            const SizedBox(height: AppDimensions.paddingM),
            
            // Progress
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Progress',
                            style: AppTextStyles.bodySmall,
                          ),
                          Text(
                            '${(progress[index] * 100).toInt()}%',
                            style: AppTextStyles.bodySmall.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: AppDimensions.paddingXS),
                      LinearProgressIndicator(
                        value: progress[index],
                        backgroundColor: AppColors.surfaceVariant,
                        valueColor: AlwaysStoppedAnimation<Color>(colors[index]),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppDimensions.paddingM),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: 'Continue',
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Study plan details coming soon!')),
                      );
                    },
                    height: 36,
                  ),
                ),
                const SizedBox(width: AppDimensions.paddingS),
                CustomButton(
                  text: 'Review',
                  isOutlined: true,
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Review feature coming soon!')),
                    );
                  },
                  width: 80,
                  height: 36,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showCreateStudyPlanDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create Study Plan'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Choose how you want to create your study plan:'),
            SizedBox(height: AppDimensions.paddingL),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Upload syllabus feature coming soon!')),
              );
            },
            child: const Text('Upload Syllabus'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Manual creation feature coming soon!')),
              );
            },
            child: const Text('Create Manually'),
          ),
        ],
      ),
    );
  }
}
