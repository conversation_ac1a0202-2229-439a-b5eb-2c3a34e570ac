import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/user_controller.dart';
import '../../constants/app_constants.dart';
import '../../widgets/custom_button.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Profile'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              _showSettingsDialog(context);
            },
          ),
        ],
      ),
      body: Consumer2<AuthController, UserController>(
        builder: (context, authController, userController, child) {
          final user = authController.currentUser;
          final stats = userController.stats;
          
          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppDimensions.paddingL),
            child: Column(
              children: [
                // Profile Header
                _buildProfileHeader(user?.fullName ?? 'Student', user?.email ?? ''),
                
                const SizedBox(height: AppDimensions.paddingL),
                
                // Quick Stats
                _buildQuickStats(stats),
                
                const SizedBox(height: AppDimensions.paddingL),
                
                // Menu Items
                _buildMenuItems(context, authController, userController),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildProfileHeader(String name, String email) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(40),
              ),
              child: const Icon(
                Icons.person,
                color: Colors.white,
                size: 40,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Text(
              name,
              style: AppTextStyles.headline2,
            ),
            Text(
              email,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            CustomButton(
              text: 'Edit Profile',
              isOutlined: true,
              onPressed: () {
                // TODO: Navigate to edit profile
              },
              width: 120,
              height: 36,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(UserStats stats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Your Stats',
              style: AppTextStyles.headline3,
            ),
            const SizedBox(height: AppDimensions.paddingL),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Study Streak',
                    '${stats.currentStreak} days',
                    Icons.local_fire_department,
                    AppColors.accent,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Total Points',
                    '${stats.totalPoints}',
                    Icons.stars,
                    AppColors.secondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Study Hours',
                    '${(stats.totalStudyTimeMinutes / 60).toStringAsFixed(1)}h',
                    Icons.access_time,
                    AppColors.primary,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Achievements',
                    '${stats.achievements.length}',
                    Icons.emoji_events,
                    AppColors.info,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          ),
          child: Icon(
            icon,
            color: color,
            size: AppDimensions.iconM,
          ),
        ),
        const SizedBox(height: AppDimensions.paddingS),
        Text(
          value,
          style: AppTextStyles.headline3.copyWith(color: color),
        ),
        Text(
          label,
          style: AppTextStyles.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildMenuItems(BuildContext context, AuthController authController, UserController userController) {
    return Column(
      children: [
        _buildMenuItem(
          'Study Preferences',
          'Customize your study settings',
          Icons.tune,
          () => _showPreferencesDialog(context, userController),
        ),
        _buildMenuItem(
          'Notifications',
          'Manage your notification settings',
          Icons.notifications,
          () => _showNotificationSettings(context, userController),
        ),
        _buildMenuItem(
          'Data & Privacy',
          'Manage your data and privacy settings',
          Icons.privacy_tip,
          () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Privacy settings coming soon!')),
            );
          },
        ),
        _buildMenuItem(
          'Help & Support',
          'Get help and contact support',
          Icons.help,
          () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Help & support coming soon!')),
            );
          },
        ),
        _buildMenuItem(
          'About',
          'App version and information',
          Icons.info,
          () => _showAboutDialog(context),
        ),
        const SizedBox(height: AppDimensions.paddingL),
        CustomButton(
          text: 'Sign Out',
          onPressed: () => _showSignOutDialog(context, authController),
          backgroundColor: AppColors.error,
        ),
      ],
    );
  }

  Widget _buildMenuItem(String title, String subtitle, IconData icon, VoidCallback onTap) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingS),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          child: Icon(
            icon,
            color: AppColors.primary,
            size: AppDimensions.iconS,
          ),
        ),
        title: Text(
          title,
          style: AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: AppTextStyles.bodySmall,
        ),
        trailing: const Icon(
          Icons.chevron_right,
          color: AppColors.textTertiary,
        ),
        onTap: onTap,
      ),
    );
  }

  void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Settings'),
        content: const Text('Settings feature coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showPreferencesDialog(BuildContext context, UserController userController) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Study Preferences'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Daily Study Goal'),
              subtitle: Text('${userController.preferences.dailyStudyGoalMinutes} minutes'),
              trailing: const Icon(Icons.edit),
              onTap: () {
                // TODO: Show time picker
              },
            ),
            ListTile(
              title: const Text('Study Pace'),
              subtitle: Text(userController.preferences.preferredStudyPace),
              trailing: const Icon(Icons.edit),
              onTap: () {
                // TODO: Show pace selector
              },
            ),
            SwitchListTile(
              title: const Text('Gamification'),
              subtitle: const Text('Enable points and achievements'),
              value: userController.preferences.gamificationEnabled,
              onChanged: (value) {
                // TODO: Update preference
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showNotificationSettings(BuildContext context, UserController userController) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notification Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: const Text('Study Reminders'),
              subtitle: const Text('Daily study reminder notifications'),
              value: userController.preferences.notificationsEnabled,
              onChanged: (value) {
                userController.updateNotificationSettings(value);
              },
            ),
            ListTile(
              title: const Text('Reminder Time'),
              subtitle: Text(userController.preferences.studyReminderTime.toString()),
              trailing: const Icon(Icons.edit),
              onTap: () {
                // TODO: Show time picker
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: AppConstants.appName,
      applicationVersion: AppConstants.appVersion,
      applicationLegalese: '© 2024 StudyForge. All rights reserved.',
      children: [
        const SizedBox(height: AppDimensions.paddingM),
        Text(
          AppConstants.appTagline,
          style: AppTextStyles.bodyMedium,
        ),
      ],
    );
  }

  void _showSignOutDialog(BuildContext context, AuthController authController) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await authController.signOut();
              if (context.mounted) {
                Navigator.of(context).pushReplacementNamed(AppConstants.authRoute);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }
}
