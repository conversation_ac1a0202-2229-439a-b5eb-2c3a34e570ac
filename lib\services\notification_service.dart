import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../constants/app_constants.dart';
import '../models/user_model.dart';

class NotificationService {
  static NotificationService? _instance;
  static NotificationService get instance => _instance ??= NotificationService._();
  
  NotificationService._();

  final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();
  bool _isInitialized = false;

  // Initialize notifications
  Future<void> initialize() async {
    if (_isInitialized) return;

    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    _isInitialized = true;
  }

  // Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    // Handle notification tap based on payload
    final payload = response.payload;
    if (payload != null) {
      // Navigate to appropriate screen based on payload
      // This would be implemented with navigation service
      debugPrint('Notification tapped with payload: $payload');
    }
  }

  // Request permissions
  Future<bool> requestPermissions() async {
    await initialize();

    final androidPlugin = _notifications.resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin>();
    
    if (androidPlugin != null) {
      final granted = await androidPlugin.requestNotificationsPermission();
      return granted ?? false;
    }

    final iosPlugin = _notifications.resolvePlatformSpecificImplementation<
        IOSFlutterLocalNotificationsPlugin>();
    
    if (iosPlugin != null) {
      final granted = await iosPlugin.requestPermissions(
        alert: true,
        badge: true,
        sound: true,
      );
      return granted ?? false;
    }

    return true; // Default to true for other platforms
  }

  // Schedule daily study reminder
  Future<void> scheduleDailyStudyReminder(UserPreferences preferences) async {
    if (!preferences.notificationsEnabled) return;

    await _notifications.zonedSchedule(
      AppConstants.dailyStudyReminderId,
      'Time to Study! 📚',
      'Your daily study session is waiting. Let\'s achieve your goals!',
      _nextInstanceOfTime(preferences.studyReminderTime),
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'study_reminders',
          'Study Reminders',
          channelDescription: 'Daily study reminder notifications',
          importance: Importance.high,
          priority: Priority.high,
          icon: '@mipmap/ic_launcher',
        ),
        iOS: DarwinNotificationDetails(
          categoryIdentifier: 'study_reminder',
        ),
      ),
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
      matchDateTimeComponents: DateTimeComponents.time,
      payload: 'study_reminder',
    );
  }

  // Schedule flashcard review reminder
  Future<void> scheduleFlashcardReview({
    required DateTime scheduledTime,
    required int cardCount,
    String? deckName,
  }) async {
    await _notifications.zonedSchedule(
      AppConstants.flashcardReviewId,
      'Flashcard Review Time! 🎯',
      deckName != null 
          ? 'Review $cardCount cards in "$deckName"'
          : 'You have $cardCount cards ready for review',
      scheduledTime,
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'flashcard_reviews',
          'Flashcard Reviews',
          channelDescription: 'Flashcard review reminder notifications',
          importance: Importance.high,
          priority: Priority.high,
          icon: '@mipmap/ic_launcher',
        ),
        iOS: DarwinNotificationDetails(
          categoryIdentifier: 'flashcard_review',
        ),
      ),
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
      payload: 'flashcard_review',
    );
  }

  // Schedule exam countdown
  Future<void> scheduleExamCountdown({
    required DateTime examDate,
    required String examName,
    required int daysBeforeExam,
  }) async {
    final notificationDate = examDate.subtract(Duration(days: daysBeforeExam));
    
    if (notificationDate.isBefore(DateTime.now())) return;

    await _notifications.zonedSchedule(
      AppConstants.examCountdownId + daysBeforeExam,
      'Exam Alert! ⏰',
      '$examName is in $daysBeforeExam ${daysBeforeExam == 1 ? 'day' : 'days'}. Are you ready?',
      notificationDate,
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'exam_countdown',
          'Exam Countdown',
          channelDescription: 'Exam countdown notifications',
          importance: Importance.max,
          priority: Priority.max,
          icon: '@mipmap/ic_launcher',
        ),
        iOS: DarwinNotificationDetails(
          categoryIdentifier: 'exam_countdown',
        ),
      ),
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
      payload: 'exam_countdown:$examName',
    );
  }

  // Show immediate notification
  Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    await _notifications.show(
      id,
      title,
      body,
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'general',
          'General Notifications',
          channelDescription: 'General app notifications',
          importance: Importance.defaultImportance,
          priority: Priority.defaultPriority,
          icon: '@mipmap/ic_launcher',
        ),
        iOS: DarwinNotificationDetails(),
      ),
      payload: payload,
    );
  }

  // Show achievement notification
  Future<void> showAchievementNotification({
    required String title,
    required String description,
  }) async {
    await showNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: '🏆 Achievement Unlocked!',
      body: '$title - $description',
      payload: 'achievement',
    );
  }

  // Show study streak notification
  Future<void> showStreakNotification(int streakDays) async {
    await showNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: '🔥 Study Streak!',
      body: 'Amazing! You\'ve studied for $streakDays consecutive days!',
      payload: 'streak',
    );
  }

  // Cancel specific notification
  Future<void> cancelNotification(int id) async {
    await _notifications.cancel(id);
  }

  // Cancel all notifications
  Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  // Get pending notifications
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _notifications.pendingNotificationRequests();
  }

  // Helper method to get next instance of a specific time
  DateTime _nextInstanceOfTime(TimeOfDay time) {
    final now = DateTime.now();
    var scheduledDate = DateTime(
      now.year,
      now.month,
      now.day,
      time.hour,
      time.minute,
    );

    if (scheduledDate.isBefore(now)) {
      scheduledDate = scheduledDate.add(const Duration(days: 1));
    }

    return scheduledDate;
  }

  // Update notification settings
  Future<void> updateNotificationSettings(UserPreferences preferences) async {
    if (preferences.notificationsEnabled) {
      await scheduleDailyStudyReminder(preferences);
    } else {
      await cancelNotification(AppConstants.dailyStudyReminderId);
    }
  }

  // Schedule multiple exam reminders
  Future<void> scheduleExamReminders({
    required DateTime examDate,
    required String examName,
  }) async {
    // Schedule reminders for 7 days, 3 days, and 1 day before exam
    final reminderDays = [7, 3, 1];
    
    for (final days in reminderDays) {
      await scheduleExamCountdown(
        examDate: examDate,
        examName: examName,
        daysBeforeExam: days,
      );
    }
  }

  // Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    // This would check system-level notification permissions
    // For now, return true as a placeholder
    return true;
  }
}
