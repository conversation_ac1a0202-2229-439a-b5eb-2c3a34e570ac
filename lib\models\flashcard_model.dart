import 'package:hive/hive.dart';

part 'flashcard_model.g.dart';

@HiveType(typeId: 11)
class FlashcardDeckModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String userId;

  @HiveField(2)
  String title;

  @HiveField(3)
  String? description;

  @HiveField(4)
  String? subject;

  @HiveField(5)
  List<FlashcardModel> cards;

  @HiveField(6)
  DateTime createdAt;

  @HiveField(7)
  DateTime updatedAt;

  @HiveField(8)
  DeckStatus status;

  @HiveField(9)
  int totalReviews;

  @HiveField(10)
  double averageScore;

  @HiveField(11)
  String? studyPlanId;

  FlashcardDeckModel({
    required this.id,
    required this.userId,
    required this.title,
    this.description,
    this.subject,
    this.cards = const [],
    required this.createdAt,
    required this.updatedAt,
    this.status = DeckStatus.active,
    this.totalReviews = 0,
    this.averageScore = 0.0,
    this.studyPlanId,
  });

  factory FlashcardDeckModel.fromJson(Map<String, dynamic> json) {
    return FlashcardDeckModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      subject: json['subject'] as String?,
      cards: (json['cards'] as List<dynamic>?)
              ?.map((e) => FlashcardModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      status: DeckStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => DeckStatus.active,
      ),
      totalReviews: json['total_reviews'] as int? ?? 0,
      averageScore: (json['average_score'] as num?)?.toDouble() ?? 0.0,
      studyPlanId: json['study_plan_id'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'description': description,
      'subject': subject,
      'cards': cards.map((e) => e.toJson()).toList(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'status': status.name,
      'total_reviews': totalReviews,
      'average_score': averageScore,
      'study_plan_id': studyPlanId,
    };
  }

  FlashcardDeckModel copyWith({
    String? id,
    String? userId,
    String? title,
    String? description,
    String? subject,
    List<FlashcardModel>? cards,
    DateTime? createdAt,
    DateTime? updatedAt,
    DeckStatus? status,
    int? totalReviews,
    double? averageScore,
    String? studyPlanId,
  }) {
    return FlashcardDeckModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      description: description ?? this.description,
      subject: subject ?? this.subject,
      cards: cards ?? this.cards,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      status: status ?? this.status,
      totalReviews: totalReviews ?? this.totalReviews,
      averageScore: averageScore ?? this.averageScore,
      studyPlanId: studyPlanId ?? this.studyPlanId,
    );
  }

  // Helper methods
  int get totalCards => cards.length;
  int get newCards => cards.where((c) => c.reviewCount == 0).length;
  int get dueCards => cards.where((c) => c.isDue).length;
  List<FlashcardModel> get cardsToReview => cards.where((c) => c.isDue).toList();
}

@HiveType(typeId: 12)
enum DeckStatus {
  @HiveField(0)
  active,
  @HiveField(1)
  archived,
  @HiveField(2)
  shared,
}

@HiveType(typeId: 13)
class FlashcardModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String deckId;

  @HiveField(2)
  String front;

  @HiveField(3)
  String back;

  @HiveField(4)
  String? hint;

  @HiveField(5)
  List<String>? tags;

  @HiveField(6)
  CardType type;

  @HiveField(7)
  DateTime createdAt;

  @HiveField(8)
  DateTime updatedAt;

  @HiveField(9)
  DateTime? nextReviewDate;

  @HiveField(10)
  int reviewCount;

  @HiveField(11)
  int correctCount;

  @HiveField(12)
  double easeFactor;

  @HiveField(13)
  int intervalDays;

  @HiveField(14)
  CardDifficulty lastDifficulty;

  @HiveField(15)
  bool isLearning;

  FlashcardModel({
    required this.id,
    required this.deckId,
    required this.front,
    required this.back,
    this.hint,
    this.tags,
    this.type = CardType.basic,
    required this.createdAt,
    required this.updatedAt,
    this.nextReviewDate,
    this.reviewCount = 0,
    this.correctCount = 0,
    this.easeFactor = 2.5,
    this.intervalDays = 1,
    this.lastDifficulty = CardDifficulty.medium,
    this.isLearning = true,
  });

  factory FlashcardModel.fromJson(Map<String, dynamic> json) {
    return FlashcardModel(
      id: json['id'] as String,
      deckId: json['deck_id'] as String,
      front: json['front'] as String,
      back: json['back'] as String,
      hint: json['hint'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      type: CardType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => CardType.basic,
      ),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      nextReviewDate: json['next_review_date'] != null
          ? DateTime.parse(json['next_review_date'] as String)
          : null,
      reviewCount: json['review_count'] as int? ?? 0,
      correctCount: json['correct_count'] as int? ?? 0,
      easeFactor: (json['ease_factor'] as num?)?.toDouble() ?? 2.5,
      intervalDays: json['interval_days'] as int? ?? 1,
      lastDifficulty: CardDifficulty.values.firstWhere(
        (e) => e.name == json['last_difficulty'],
        orElse: () => CardDifficulty.medium,
      ),
      isLearning: json['is_learning'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'deck_id': deckId,
      'front': front,
      'back': back,
      'hint': hint,
      'tags': tags,
      'type': type.name,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'next_review_date': nextReviewDate?.toIso8601String(),
      'review_count': reviewCount,
      'correct_count': correctCount,
      'ease_factor': easeFactor,
      'interval_days': intervalDays,
      'last_difficulty': lastDifficulty.name,
      'is_learning': isLearning,
    };
  }

  FlashcardModel copyWith({
    String? id,
    String? deckId,
    String? front,
    String? back,
    String? hint,
    List<String>? tags,
    CardType? type,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? nextReviewDate,
    int? reviewCount,
    int? correctCount,
    double? easeFactor,
    int? intervalDays,
    CardDifficulty? lastDifficulty,
    bool? isLearning,
  }) {
    return FlashcardModel(
      id: id ?? this.id,
      deckId: deckId ?? this.deckId,
      front: front ?? this.front,
      back: back ?? this.back,
      hint: hint ?? this.hint,
      tags: tags ?? this.tags,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      nextReviewDate: nextReviewDate ?? this.nextReviewDate,
      reviewCount: reviewCount ?? this.reviewCount,
      correctCount: correctCount ?? this.correctCount,
      easeFactor: easeFactor ?? this.easeFactor,
      intervalDays: intervalDays ?? this.intervalDays,
      lastDifficulty: lastDifficulty ?? this.lastDifficulty,
      isLearning: isLearning ?? this.isLearning,
    );
  }

  // Helper methods
  bool get isDue {
    if (nextReviewDate == null) return true;
    return DateTime.now().isAfter(nextReviewDate!);
  }

  double get successRate {
    if (reviewCount == 0) return 0.0;
    return correctCount / reviewCount;
  }

  bool get isNew => reviewCount == 0;
}

@HiveType(typeId: 14)
enum CardType {
  @HiveField(0)
  basic,
  @HiveField(1)
  cloze,
  @HiveField(2)
  multipleChoice,
  @HiveField(3)
  imageOcclusion,
}

@HiveType(typeId: 15)
enum CardDifficulty {
  @HiveField(0)
  easy,
  @HiveField(1)
  medium,
  @HiveField(2)
  hard,
  @HiveField(3)
  again,
}
