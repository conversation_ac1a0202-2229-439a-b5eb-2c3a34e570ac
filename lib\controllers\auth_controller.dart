import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/supabase_service.dart';
import '../models/user_model.dart';

enum AuthState { initial, loading, authenticated, unauthenticated, error }

class AuthController extends ChangeNotifier {
  final SupabaseService _supabaseService = SupabaseService.instance;

  AuthState _state = AuthState.initial;
  String? _errorMessage;
  UserModel? _currentUser;

  // Getters
  AuthState get state => _state;
  String? get errorMessage => _errorMessage;
  UserModel? get currentUser => _currentUser;
  bool get isAuthenticated => _state == AuthState.authenticated;
  bool get isLoading => _state == AuthState.loading;

  AuthController() {
    _initializeAuth();
  }

  void _initializeAuth() {
    // Check if user is already authenticated
    final user = _supabaseService.currentUser;
    if (user != null) {
      _loadUserProfile(user.id);
    } else {
      _setState(AuthState.unauthenticated);
    }

    // Listen to auth state changes
    _supabaseService.client.auth.onAuthStateChange.listen((data) {
      final AuthChangeEvent event = data.event;
      final Session? session = data.session;

      switch (event) {
        case AuthChangeEvent.signedIn:
          if (session?.user != null) {
            _loadUserProfile(session!.user.id);
          }
          break;
        case AuthChangeEvent.signedOut:
          _currentUser = null;
          _setState(AuthState.unauthenticated);
          break;
        case AuthChangeEvent.userUpdated:
          if (session?.user != null) {
            _loadUserProfile(session!.user.id);
          }
          break;
        default:
          break;
      }
    });
  }

  Future<void> _loadUserProfile(String userId) async {
    try {
      final userProfile = await _supabaseService.getUserProfile(userId);
      if (userProfile != null) {
        _currentUser = userProfile;
        _setState(AuthState.authenticated);
      } else {
        _setState(AuthState.unauthenticated);
      }
    } catch (e) {
      _setError('Failed to load user profile: $e');
    }
  }

  Future<bool> signUp({
    required String email,
    required String password,
    String? fullName,
  }) async {
    try {
      _setState(AuthState.loading);

      final response = await _supabaseService.signUp(
        email: email,
        password: password,
        fullName: fullName,
      );

      if (response.user != null) {
        // User created successfully
        // Note: User might need to verify email before being fully authenticated
        return true;
      } else {
        _setError('Failed to create account');
        return false;
      }
    } on AuthException catch (e) {
      _setError(e.message);
      return false;
    } catch (e) {
      _setError('An unexpected error occurred: $e');
      return false;
    }
  }

  Future<bool> signIn({required String email, required String password}) async {
    try {
      _setState(AuthState.loading);

      final response = await _supabaseService.signIn(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // Authentication successful, user profile will be loaded via auth state listener
        return true;
      } else {
        _setError('Failed to sign in');
        return false;
      }
    } on AuthException catch (e) {
      _setError(e.message);
      return false;
    } catch (e) {
      _setError('An unexpected error occurred: $e');
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      _setState(AuthState.loading);
      await _supabaseService.signOut();
      // Auth state listener will handle the state change
    } catch (e) {
      _setError('Failed to sign out: $e');
    }
  }

  Future<bool> resetPassword(String email) async {
    try {
      _setState(AuthState.loading);
      await _supabaseService.resetPassword(email);
      _setState(AuthState.unauthenticated);
      return true;
    } on AuthException catch (e) {
      _setError(e.message);
      return false;
    } catch (e) {
      _setError('Failed to reset password: $e');
      return false;
    }
  }

  Future<bool> updateProfile({String? fullName, String? avatarUrl}) async {
    if (_currentUser == null) return false;

    try {
      _setState(AuthState.loading);

      final updatedUser = _currentUser!.copyWith(
        fullName: fullName ?? _currentUser!.fullName,
        avatarUrl: avatarUrl ?? _currentUser!.avatarUrl,
        lastLoginAt: DateTime.now(),
      );

      await _supabaseService.updateUserProfile(updatedUser);
      _currentUser = updatedUser;
      _setState(AuthState.authenticated);
      return true;
    } catch (e) {
      _setError('Failed to update profile: $e');
      return false;
    }
  }

  Future<bool> updateUserPreferences(UserPreferences preferences) async {
    if (_currentUser == null) return false;

    try {
      final updatedUser = _currentUser!.copyWith(
        preferences: preferences,
        lastLoginAt: DateTime.now(),
      );

      await _supabaseService.updateUserProfile(updatedUser);
      _currentUser = updatedUser;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to update preferences: $e');
      return false;
    }
  }

  Future<bool> updateUserStats(UserStats stats) async {
    if (_currentUser == null) return false;

    try {
      final updatedUser = _currentUser!.copyWith(
        stats: stats,
        lastLoginAt: DateTime.now(),
      );

      await _supabaseService.updateUserProfile(updatedUser);
      _currentUser = updatedUser;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to update stats: $e');
      return false;
    }
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void _setState(AuthState newState) {
    _state = newState;
    if (newState != AuthState.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _state = AuthState.error;
    notifyListeners();
  }
}
