// Environment Configuration
// In a production app, these should be loaded from environment variables
// or a secure configuration service

class EnvConfig {
  // Supabase Configuration
  static const String supabaseUrl = 'https://your-project.supabase.co';
  static const String supabaseAnonKey = 'your-supabase-anon-key';
  
  // Gemini AI Configuration
  static const String geminiApiKey = 'your-gemini-api-key';
  
  // App Configuration
  static const bool isProduction = false;
  static const bool enableLogging = true;
  
  // Feature Flags
  static const bool enableGamification = true;
  static const bool enableNotifications = true;
  static const bool enableAIFeatures = true;
  
  // API Endpoints
  static const String baseApiUrl = 'https://api.studyforge.com';
  
  // Storage Configuration
  static const String storageBucket = 'study-materials';
  
  // Validation
  static bool get isConfigured {
    return supabaseUrl.isNotEmpty && 
           supabaseAnonKey.isNotEmpty && 
           geminiApiKey.isNotEmpty;
  }
  
  static void validateConfig() {
    if (!isConfigured) {
      throw Exception(
        'App configuration is incomplete. Please check your environment variables.'
      );
    }
  }
}
