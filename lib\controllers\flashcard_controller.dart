import 'package:flutter/material.dart';
import '../models/flashcard_model.dart';
import '../services/supabase_service.dart';
import '../services/gemini_service.dart';
import '../constants/app_constants.dart';

enum FlashcardState {
  initial,
  loading,
  loaded,
  creating,
  studying,
  error,
}

class FlashcardController extends ChangeNotifier {
  final SupabaseService _supabaseService = SupabaseService.instance;
  final GeminiService _geminiService = GeminiService.instance;

  FlashcardState _state = FlashcardState.initial;
  List<FlashcardDeckModel> _decks = [];
  FlashcardDeckModel? _currentDeck;
  List<FlashcardModel> _reviewCards = [];
  int _currentCardIndex = 0;
  String? _errorMessage;

  // Getters
  FlashcardState get state => _state;
  List<FlashcardDeckModel> get decks => _decks;
  FlashcardDeckModel? get currentDeck => _currentDeck;
  List<FlashcardModel> get reviewCards => _reviewCards;
  int get currentCardIndex => _currentCardIndex;
  FlashcardModel? get currentCard => 
      _reviewCards.isNotEmpty && _currentCardIndex < _reviewCards.length 
          ? _reviewCards[_currentCardIndex] 
          : null;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _state == FlashcardState.loading || _state == FlashcardState.creating;
  bool get isStudying => _state == FlashcardState.studying;

  // Load all decks for user
  Future<void> loadDecks(String userId) async {
    try {
      _setState(FlashcardState.loading);
      
      final data = await _supabaseService.select(
        table: 'flashcard_decks',
        filters: {'user_id': userId},
        orderBy: 'created_at',
        ascending: false,
      );

      _decks = data.map((item) => FlashcardDeckModel.fromJson(item)).toList();
      _setState(FlashcardState.loaded);
    } catch (e) {
      _setError('Failed to load flashcard decks: $e');
    }
  }

  // Create deck with AI-generated flashcards
  Future<FlashcardDeckModel?> createDeckWithAI({
    required String userId,
    required String title,
    required String content,
    String? description,
    String? subject,
    String? studyPlanId,
    int maxCards = 20,
  }) async {
    try {
      _setState(FlashcardState.creating);

      // Generate flashcards with AI
      final deckId = DateTime.now().millisecondsSinceEpoch.toString();
      final cards = await _geminiService.generateFlashcards(
        content: content,
        deckId: deckId,
        maxCards: maxCards,
      );

      // Create deck model
      final deck = FlashcardDeckModel(
        id: deckId,
        userId: userId,
        title: title,
        description: description,
        subject: subject,
        cards: cards,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        studyPlanId: studyPlanId,
      );

      // Save to database
      final savedData = await _supabaseService.insert(
        table: 'flashcard_decks',
        data: deck.toJson(),
      );

      final savedDeck = FlashcardDeckModel.fromJson(savedData);
      
      // Add to local list
      _decks.insert(0, savedDeck);
      _currentDeck = savedDeck;
      
      _setState(FlashcardState.loaded);
      return savedDeck;
    } catch (e) {
      _setError('Failed to create flashcard deck: $e');
      return null;
    }
  }

  // Create manual deck
  Future<FlashcardDeckModel?> createManualDeck({
    required String userId,
    required String title,
    String? description,
    String? subject,
    String? studyPlanId,
  }) async {
    try {
      _setState(FlashcardState.creating);

      final deck = FlashcardDeckModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        title: title,
        description: description,
        subject: subject,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        studyPlanId: studyPlanId,
      );

      // Save to database
      final savedData = await _supabaseService.insert(
        table: 'flashcard_decks',
        data: deck.toJson(),
      );

      final savedDeck = FlashcardDeckModel.fromJson(savedData);
      
      // Add to local list
      _decks.insert(0, savedDeck);
      _currentDeck = savedDeck;
      
      _setState(FlashcardState.loaded);
      return savedDeck;
    } catch (e) {
      _setError('Failed to create flashcard deck: $e');
      return null;
    }
  }

  // Add card to deck
  Future<bool> addCardToDeck({
    required String deckId,
    required String front,
    required String back,
    String? hint,
    List<String>? tags,
    CardType type = CardType.basic,
  }) async {
    try {
      final deck = _decks.firstWhere((d) => d.id == deckId);
      final cards = List<FlashcardModel>.from(deck.cards);

      final card = FlashcardModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        deckId: deckId,
        front: front,
        back: back,
        hint: hint,
        tags: tags,
        type: type,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      cards.add(card);

      final updatedDeck = deck.copyWith(
        cards: cards,
        updatedAt: DateTime.now(),
      );

      return await updateDeck(updatedDeck);
    } catch (e) {
      _setError('Failed to add card to deck: $e');
      return false;
    }
  }

  // Update deck
  Future<bool> updateDeck(FlashcardDeckModel deck) async {
    try {
      _setState(FlashcardState.loading);

      final updatedDeck = deck.copyWith(updatedAt: DateTime.now());

      await _supabaseService.update(
        table: 'flashcard_decks',
        data: updatedDeck.toJson(),
        filters: {'id': deck.id},
      );

      // Update local list
      final index = _decks.indexWhere((d) => d.id == deck.id);
      if (index != -1) {
        _decks[index] = updatedDeck;
      }

      if (_currentDeck?.id == deck.id) {
        _currentDeck = updatedDeck;
      }

      _setState(FlashcardState.loaded);
      return true;
    } catch (e) {
      _setError('Failed to update deck: $e');
      return false;
    }
  }

  // Delete deck
  Future<bool> deleteDeck(String deckId) async {
    try {
      _setState(FlashcardState.loading);

      await _supabaseService.delete(
        table: 'flashcard_decks',
        filters: {'id': deckId},
      );

      // Remove from local list
      _decks.removeWhere((deck) => deck.id == deckId);

      if (_currentDeck?.id == deckId) {
        _currentDeck = null;
      }

      _setState(FlashcardState.loaded);
      return true;
    } catch (e) {
      _setError('Failed to delete deck: $e');
      return false;
    }
  }

  // Start review session
  void startReviewSession(FlashcardDeckModel deck) {
    _currentDeck = deck;
    _reviewCards = deck.cardsToReview;
    _currentCardIndex = 0;
    _setState(FlashcardState.studying);
  }

  // Answer card and apply spaced repetition
  Future<void> answerCard(CardDifficulty difficulty) async {
    if (currentCard == null) return;

    try {
      final card = currentCard!;
      final updatedCard = _applySpacedRepetition(card, difficulty);
      
      // Update card in deck
      final deck = _currentDeck!;
      final cards = List<FlashcardModel>.from(deck.cards);
      final cardIndex = cards.indexWhere((c) => c.id == card.id);
      
      if (cardIndex != -1) {
        cards[cardIndex] = updatedCard;
        
        // Update deck stats
        final totalReviews = deck.totalReviews + 1;
        final correctAnswers = difficulty == CardDifficulty.easy || difficulty == CardDifficulty.medium 
            ? deck.totalReviews * deck.averageScore + 1 
            : deck.totalReviews * deck.averageScore;
        final averageScore = correctAnswers / totalReviews;

        final updatedDeck = deck.copyWith(
          cards: cards,
          totalReviews: totalReviews,
          averageScore: averageScore,
          updatedAt: DateTime.now(),
        );

        await updateDeck(updatedDeck);
      }

      // Move to next card
      _currentCardIndex++;
      
      // Check if review session is complete
      if (_currentCardIndex >= _reviewCards.length) {
        _setState(FlashcardState.loaded);
        _reviewCards.clear();
        _currentCardIndex = 0;
      } else {
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to process card answer: $e');
    }
  }

  // Apply spaced repetition algorithm
  FlashcardModel _applySpacedRepetition(FlashcardModel card, CardDifficulty difficulty) {
    final now = DateTime.now();
    int newInterval = card.intervalDays;
    double newEaseFactor = card.easeFactor;
    bool isLearning = card.isLearning;

    switch (difficulty) {
      case CardDifficulty.again:
        newInterval = 1;
        isLearning = true;
        newEaseFactor = (card.easeFactor - 0.2).clamp(1.3, 2.5);
        break;
      case CardDifficulty.hard:
        newInterval = (card.intervalDays * 1.2).round();
        newEaseFactor = (card.easeFactor - 0.15).clamp(1.3, 2.5);
        break;
      case CardDifficulty.medium:
        if (isLearning) {
          newInterval = AppConstants.spacedRepetitionIntervals[
              (AppConstants.spacedRepetitionIntervals.indexOf(card.intervalDays) + 1)
                  .clamp(0, AppConstants.spacedRepetitionIntervals.length - 1)];
          if (newInterval >= 7) isLearning = false;
        } else {
          newInterval = (card.intervalDays * card.easeFactor).round();
        }
        break;
      case CardDifficulty.easy:
        if (isLearning) {
          newInterval = 4;
          isLearning = false;
        } else {
          newInterval = (card.intervalDays * card.easeFactor * 1.3).round();
        }
        newEaseFactor = card.easeFactor + 0.15;
        break;
    }

    return card.copyWith(
      reviewCount: card.reviewCount + 1,
      correctCount: difficulty != CardDifficulty.again ? card.correctCount + 1 : card.correctCount,
      intervalDays: newInterval,
      easeFactor: newEaseFactor,
      lastDifficulty: difficulty,
      isLearning: isLearning,
      nextReviewDate: now.add(Duration(days: newInterval)),
      updatedAt: now,
    );
  }

  // Get cards due for review
  List<FlashcardModel> getDueCards() {
    return _decks
        .expand((deck) => deck.cards)
        .where((card) => card.isDue)
        .toList();
  }

  // Get new cards
  List<FlashcardModel> getNewCards() {
    return _decks
        .expand((deck) => deck.cards)
        .where((card) => card.isNew)
        .toList();
  }

  // Search decks
  List<FlashcardDeckModel> searchDecks(String query) {
    if (query.isEmpty) return _decks;
    
    return _decks.where((deck) {
      return deck.title.toLowerCase().contains(query.toLowerCase()) ||
             (deck.subject?.toLowerCase().contains(query.toLowerCase()) ?? false) ||
             (deck.description?.toLowerCase().contains(query.toLowerCase()) ?? false);
    }).toList();
  }

  // Set current deck
  void setCurrentDeck(FlashcardDeckModel? deck) {
    _currentDeck = deck;
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void _setState(FlashcardState newState) {
    _state = newState;
    if (newState != FlashcardState.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _state = FlashcardState.error;
    notifyListeners();
  }

  @override
  void dispose() {
    super.dispose();
  }
}
