import 'package:hive/hive.dart';

part 'study_plan_model.g.dart';

@HiveType(typeId: 4)
class StudyPlanModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String userId;

  @HiveField(2)
  String title;

  @HiveField(3)
  String? description;

  @HiveField(4)
  String subject;

  @HiveField(5)
  DateTime startDate;

  @HiveField(6)
  DateTime endDate;

  @HiveField(7)
  DateTime? examDate;

  @HiveField(8)
  StudyPlanStatus status;

  @HiveField(9)
  List<StudyTopicModel> topics;

  @HiveField(10)
  List<StudySessionModel> sessions;

  @HiveField(11)
  DateTime createdAt;

  @HiveField(12)
  DateTime updatedAt;

  @HiveField(13)
  String? syllabusContent;

  @HiveField(14)
  double progress; // 0.0 to 1.0

  StudyPlanModel({
    required this.id,
    required this.userId,
    required this.title,
    this.description,
    required this.subject,
    required this.startDate,
    required this.endDate,
    this.examDate,
    this.status = StudyPlanStatus.active,
    this.topics = const [],
    this.sessions = const [],
    required this.createdAt,
    required this.updatedAt,
    this.syllabusContent,
    this.progress = 0.0,
  });

  factory StudyPlanModel.fromJson(Map<String, dynamic> json) {
    return StudyPlanModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      subject: json['subject'] as String,
      startDate: DateTime.parse(json['start_date'] as String),
      endDate: DateTime.parse(json['end_date'] as String),
      examDate:
          json['exam_date'] != null
              ? DateTime.parse(json['exam_date'] as String)
              : null,
      status: StudyPlanStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => StudyPlanStatus.active,
      ),
      topics:
          (json['topics'] as List<dynamic>?)
              ?.map((e) => StudyTopicModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      sessions:
          (json['sessions'] as List<dynamic>?)
              ?.map(
                (e) => StudySessionModel.fromJson(e as Map<String, dynamic>),
              )
              .toList() ??
          [],
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      syllabusContent: json['syllabus_content'] as String?,
      progress: (json['progress'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'description': description,
      'subject': subject,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'exam_date': examDate?.toIso8601String(),
      'status': status.name,
      'topics': topics.map((e) => e.toJson()).toList(),
      'sessions': sessions.map((e) => e.toJson()).toList(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'syllabus_content': syllabusContent,
      'progress': progress,
    };
  }

  StudyPlanModel copyWith({
    String? id,
    String? userId,
    String? title,
    String? description,
    String? subject,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? examDate,
    StudyPlanStatus? status,
    List<StudyTopicModel>? topics,
    List<StudySessionModel>? sessions,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? syllabusContent,
    double? progress,
  }) {
    return StudyPlanModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      description: description ?? this.description,
      subject: subject ?? this.subject,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      examDate: examDate ?? this.examDate,
      status: status ?? this.status,
      topics: topics ?? this.topics,
      sessions: sessions ?? this.sessions,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syllabusContent: syllabusContent ?? this.syllabusContent,
      progress: progress ?? this.progress,
    );
  }
}

@HiveType(typeId: 5)
enum StudyPlanStatus {
  @HiveField(0)
  active,
  @HiveField(1)
  completed,
  @HiveField(2)
  paused,
  @HiveField(3)
  archived,
}

@HiveType(typeId: 6)
class StudyTopicModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String studyPlanId;

  @HiveField(2)
  String title;

  @HiveField(3)
  String? description;

  @HiveField(4)
  int orderIndex;

  @HiveField(5)
  int estimatedHours;

  @HiveField(6)
  TopicStatus status;

  @HiveField(7)
  DateTime? scheduledDate;

  @HiveField(8)
  DateTime? completedDate;

  @HiveField(9)
  List<String> subtopics;

  @HiveField(10)
  TopicPriority priority;

  StudyTopicModel({
    required this.id,
    required this.studyPlanId,
    required this.title,
    this.description,
    required this.orderIndex,
    this.estimatedHours = 1,
    this.status = TopicStatus.notStarted,
    this.scheduledDate,
    this.completedDate,
    this.subtopics = const [],
    this.priority = TopicPriority.medium,
  });

  factory StudyTopicModel.fromJson(Map<String, dynamic> json) {
    return StudyTopicModel(
      id: json['id'] as String,
      studyPlanId: json['study_plan_id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      orderIndex: json['order_index'] as int,
      estimatedHours: json['estimated_hours'] as int? ?? 1,
      status: TopicStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => TopicStatus.notStarted,
      ),
      scheduledDate:
          json['scheduled_date'] != null
              ? DateTime.parse(json['scheduled_date'] as String)
              : null,
      completedDate:
          json['completed_date'] != null
              ? DateTime.parse(json['completed_date'] as String)
              : null,
      subtopics:
          (json['subtopics'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      priority: TopicPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => TopicPriority.medium,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'study_plan_id': studyPlanId,
      'title': title,
      'description': description,
      'order_index': orderIndex,
      'estimated_hours': estimatedHours,
      'status': status.name,
      'scheduled_date': scheduledDate?.toIso8601String(),
      'completed_date': completedDate?.toIso8601String(),
      'subtopics': subtopics,
      'priority': priority.name,
    };
  }

  StudyTopicModel copyWith({
    String? id,
    String? studyPlanId,
    String? title,
    String? description,
    int? orderIndex,
    int? estimatedHours,
    TopicStatus? status,
    DateTime? scheduledDate,
    DateTime? completedDate,
    List<String>? subtopics,
    TopicPriority? priority,
  }) {
    return StudyTopicModel(
      id: id ?? this.id,
      studyPlanId: studyPlanId ?? this.studyPlanId,
      title: title ?? this.title,
      description: description ?? this.description,
      orderIndex: orderIndex ?? this.orderIndex,
      estimatedHours: estimatedHours ?? this.estimatedHours,
      status: status ?? this.status,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      completedDate: completedDate ?? this.completedDate,
      subtopics: subtopics ?? this.subtopics,
      priority: priority ?? this.priority,
    );
  }
}

@HiveType(typeId: 7)
enum TopicStatus {
  @HiveField(0)
  notStarted,
  @HiveField(1)
  inProgress,
  @HiveField(2)
  completed,
  @HiveField(3)
  skipped,
}

@HiveType(typeId: 8)
enum TopicPriority {
  @HiveField(0)
  low,
  @HiveField(1)
  medium,
  @HiveField(2)
  high,
  @HiveField(3)
  critical,
}

@HiveType(typeId: 9)
class StudySessionModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String studyPlanId;

  @HiveField(2)
  String? topicId;

  @HiveField(3)
  DateTime startTime;

  @HiveField(4)
  DateTime? endTime;

  @HiveField(5)
  int durationMinutes;

  @HiveField(6)
  SessionType type;

  @HiveField(7)
  String? notes;

  @HiveField(8)
  int pointsEarned;

  StudySessionModel({
    required this.id,
    required this.studyPlanId,
    this.topicId,
    required this.startTime,
    this.endTime,
    this.durationMinutes = 0,
    this.type = SessionType.study,
    this.notes,
    this.pointsEarned = 0,
  });

  factory StudySessionModel.fromJson(Map<String, dynamic> json) {
    return StudySessionModel(
      id: json['id'] as String,
      studyPlanId: json['study_plan_id'] as String,
      topicId: json['topic_id'] as String?,
      startTime: DateTime.parse(json['start_time'] as String),
      endTime:
          json['end_time'] != null
              ? DateTime.parse(json['end_time'] as String)
              : null,
      durationMinutes: json['duration_minutes'] as int? ?? 0,
      type: SessionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => SessionType.study,
      ),
      notes: json['notes'] as String?,
      pointsEarned: json['points_earned'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'study_plan_id': studyPlanId,
      'topic_id': topicId,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'duration_minutes': durationMinutes,
      'type': type.name,
      'notes': notes,
      'points_earned': pointsEarned,
    };
  }

  StudySessionModel copyWith({
    String? id,
    String? studyPlanId,
    String? topicId,
    DateTime? startTime,
    DateTime? endTime,
    int? durationMinutes,
    SessionType? type,
    String? notes,
    int? pointsEarned,
  }) {
    return StudySessionModel(
      id: id ?? this.id,
      studyPlanId: studyPlanId ?? this.studyPlanId,
      topicId: topicId ?? this.topicId,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      type: type ?? this.type,
      notes: notes ?? this.notes,
      pointsEarned: pointsEarned ?? this.pointsEarned,
    );
  }
}

@HiveType(typeId: 10)
enum SessionType {
  @HiveField(0)
  study,
  @HiveField(1)
  review,
  @HiveField(2)
  practice,
  @HiveField(3)
  exam,
}
