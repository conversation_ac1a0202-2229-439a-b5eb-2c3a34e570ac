# StudyForge - Your Smart Study Assistant

StudyForge is a personalized learning companion mobile app that helps students create effective study plans, manage flashcards with spaced repetition, and track their learning progress using AI-powered features.

## Features

### 🎯 Smart Syllabus Breakdown

- Upload PDF/document syllabi and let AI extract course information
- Automatic topic identification and time estimation
- Intelligent study plan generation based on deadlines and preferences

### 📚 AI-Powered Study Plan Generator

- Personalized study schedules based on your pace and available time
- Adaptive planning that adjusts to your progress
- Integration with exam dates and deadlines

### 🎴 Intelligent Flashcard System

- AI-generated flashcards from your study materials
- Spaced repetition algorithm for optimal retention
- Multiple card types and difficulty tracking

### 🔔 Smart Notifications & Reminders

- Daily study reminders at your preferred time
- Flashcard review notifications
- Exam countdown alerts

### 📊 Comprehensive Progress Tracking

- Visual dashboards showing your study progress
- Detailed analytics on time spent and topics covered
- Performance insights and recommendations

### 🏆 Gamified Learning Experience

- Points and achievement system
- Study streaks and consistency tracking
- Motivational badges and milestones

## Tech Stack

- **Frontend**: Flutter (Dart)
- **Backend**: Supabase (PostgreSQL, Authentication, Storage)
- **AI Integration**: Google Gemini AI
- **Local Storage**: Hive
- **State Management**: Provider
- **Notifications**: Flutter Local Notifications

## Getting Started

### Prerequisites

- Flutter SDK (3.0 or higher)
- Dart SDK (3.0 or higher)
- Android Studio / VS Code
- Supabase account
- Google AI Studio account (for Gemini API)

### Installation

1. **Clone the repository**

   ```bash
   git clone https://github.com/yourusername/studyforge.git
   cd studyforge
   ```

2. **Install dependencies**

   ```bash
   flutter pub get
   ```

3. **Set up Supabase**

   - Create a new project at [supabase.com](https://supabase.com)
   - Run the SQL schema from `supabase_schema.sql` in your Supabase SQL editor
   - Get your project URL and anon key

4. **Set up Gemini AI**

   - Get an API key from [Google AI Studio](https://makersuite.google.com/app/apikey)

5. **Configure environment variables**

   - Update `lib/config/env_config.dart` with your API keys:

   ```dart
   static const String supabaseUrl = 'YOUR_SUPABASE_URL';
   static const String supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';
   static const String geminiApiKey = 'YOUR_GEMINI_API_KEY';
   ```

6. **Generate code (if needed)**

   ```bash
   flutter packages pub run build_runner build
   ```

7. **Run the app**
   ```bash
   flutter run
   ```
