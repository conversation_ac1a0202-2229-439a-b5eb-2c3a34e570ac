import '../models/user_model.dart';
import '../constants/app_constants.dart';
import 'notification_service.dart';

class GamificationService {
  static GamificationService? _instance;
  static GamificationService get instance => _instance ??= GamificationService._();
  
  GamificationService._();

  final NotificationService _notificationService = NotificationService.instance;

  // Calculate points for different activities
  int calculateStudySessionPoints(int durationMinutes) {
    // Base points: 1 point per minute
    int basePoints = durationMinutes;
    
    // Bonus for longer sessions
    if (durationMinutes >= 60) {
      basePoints += 20; // 1 hour bonus
    }
    if (durationMinutes >= 120) {
      basePoints += 30; // 2 hour bonus
    }
    
    return basePoints;
  }

  int calculateFlashcardPoints(int cardsReviewed, double accuracy) {
    // Base points per card
    int basePoints = cardsReviewed * AppConstants.pointsPerFlashcardReview;
    
    // Accuracy bonus
    if (accuracy >= 0.9) {
      basePoints = (basePoints * 1.5).round(); // 50% bonus for 90%+ accuracy
    } else if (accuracy >= 0.8) {
      basePoints = (basePoints * 1.2).round(); // 20% bonus for 80%+ accuracy
    }
    
    return basePoints;
  }

  int calculateStreakPoints(int streakDays) {
    if (streakDays <= 0) return 0;
    
    // Progressive streak rewards
    int points = 0;
    
    // Daily streak bonus
    points += streakDays * AppConstants.pointsPerDayStreak;
    
    // Milestone bonuses
    if (streakDays >= 7) points += 100; // Week milestone
    if (streakDays >= 30) points += 500; // Month milestone
    if (streakDays >= 100) points += 1000; // 100-day milestone
    if (streakDays >= 365) points += 5000; // Year milestone
    
    return points;
  }

  // Check and award achievements
  List<Achievement> checkForNewAchievements(UserStats oldStats, UserStats newStats) {
    final newAchievements = <Achievement>[];

    // Study time achievements
    if (oldStats.totalStudyTimeMinutes < 60 && newStats.totalStudyTimeMinutes >= 60) {
      newAchievements.add(_getAchievement('first_hour'));
    }
    if (oldStats.totalStudyTimeMinutes < 600 && newStats.totalStudyTimeMinutes >= 600) {
      newAchievements.add(_getAchievement('study_warrior'));
    }
    if (oldStats.totalStudyTimeMinutes < 3600 && newStats.totalStudyTimeMinutes >= 3600) {
      newAchievements.add(_getAchievement('study_master'));
    }

    // Streak achievements
    if (oldStats.currentStreak < 3 && newStats.currentStreak >= 3) {
      newAchievements.add(_getAchievement('three_day_streak'));
    }
    if (oldStats.currentStreak < 7 && newStats.currentStreak >= 7) {
      newAchievements.add(_getAchievement('week_streak'));
    }
    if (oldStats.currentStreak < 30 && newStats.currentStreak >= 30) {
      newAchievements.add(_getAchievement('month_streak'));
    }
    if (oldStats.currentStreak < 100 && newStats.currentStreak >= 100) {
      newAchievements.add(_getAchievement('century_streak'));
    }

    // Flashcard achievements
    if (oldStats.totalFlashcardsReviewed < 50 && newStats.totalFlashcardsReviewed >= 50) {
      newAchievements.add(_getAchievement('flashcard_novice'));
    }
    if (oldStats.totalFlashcardsReviewed < 500 && newStats.totalFlashcardsReviewed >= 500) {
      newAchievements.add(_getAchievement('flashcard_master'));
    }
    if (oldStats.totalFlashcardsReviewed < 2000 && newStats.totalFlashcardsReviewed >= 2000) {
      newAchievements.add(_getAchievement('flashcard_legend'));
    }

    // Points achievements
    if (oldStats.totalPoints < 1000 && newStats.totalPoints >= 1000) {
      newAchievements.add(_getAchievement('point_collector'));
    }
    if (oldStats.totalPoints < 10000 && newStats.totalPoints >= 10000) {
      newAchievements.add(_getAchievement('point_master'));
    }

    // Study plan achievements
    if (oldStats.totalStudyPlansCompleted < 1 && newStats.totalStudyPlansCompleted >= 1) {
      newAchievements.add(_getAchievement('first_completion'));
    }
    if (oldStats.totalStudyPlansCompleted < 5 && newStats.totalStudyPlansCompleted >= 5) {
      newAchievements.add(_getAchievement('plan_master'));
    }

    return newAchievements;
  }

  // Get achievement details
  Achievement _getAchievement(String id) {
    switch (id) {
      case 'first_hour':
        return Achievement(
          id: id,
          title: 'First Hour',
          description: 'Complete your first hour of study',
          icon: 'access_time',
          color: 'primary',
          points: 50,
        );
      case 'study_warrior':
        return Achievement(
          id: id,
          title: 'Study Warrior',
          description: 'Complete 10 hours of study time',
          icon: 'military_tech',
          color: 'secondary',
          points: 200,
        );
      case 'study_master':
        return Achievement(
          id: id,
          title: 'Study Master',
          description: 'Complete 60 hours of study time',
          icon: 'school',
          color: 'accent',
          points: 1000,
        );
      case 'three_day_streak':
        return Achievement(
          id: id,
          title: 'Getting Started',
          description: 'Study for 3 consecutive days',
          icon: 'local_fire_department',
          color: 'warning',
          points: 100,
        );
      case 'week_streak':
        return Achievement(
          id: id,
          title: 'Week Warrior',
          description: 'Study for 7 consecutive days',
          icon: 'local_fire_department',
          color: 'accent',
          points: 300,
        );
      case 'month_streak':
        return Achievement(
          id: id,
          title: 'Monthly Master',
          description: 'Study for 30 consecutive days',
          icon: 'local_fire_department',
          color: 'error',
          points: 1500,
        );
      case 'century_streak':
        return Achievement(
          id: id,
          title: 'Century Champion',
          description: 'Study for 100 consecutive days',
          icon: 'emoji_events',
          color: 'accent',
          points: 10000,
        );
      case 'flashcard_novice':
        return Achievement(
          id: id,
          title: 'Flashcard Novice',
          description: 'Review 50 flashcards',
          icon: 'style',
          color: 'info',
          points: 100,
        );
      case 'flashcard_master':
        return Achievement(
          id: id,
          title: 'Flashcard Master',
          description: 'Review 500 flashcards',
          icon: 'style',
          color: 'primary',
          points: 500,
        );
      case 'flashcard_legend':
        return Achievement(
          id: id,
          title: 'Flashcard Legend',
          description: 'Review 2000 flashcards',
          icon: 'auto_awesome',
          color: 'accent',
          points: 2000,
        );
      case 'point_collector':
        return Achievement(
          id: id,
          title: 'Point Collector',
          description: 'Earn 1000 points',
          icon: 'stars',
          color: 'secondary',
          points: 100,
        );
      case 'point_master':
        return Achievement(
          id: id,
          title: 'Point Master',
          description: 'Earn 10000 points',
          icon: 'stars',
          color: 'accent',
          points: 1000,
        );
      case 'first_completion':
        return Achievement(
          id: id,
          title: 'First Success',
          description: 'Complete your first study plan',
          icon: 'check_circle',
          color: 'success',
          points: 500,
        );
      case 'plan_master':
        return Achievement(
          id: id,
          title: 'Plan Master',
          description: 'Complete 5 study plans',
          icon: 'task_alt',
          color: 'primary',
          points: 2000,
        );
      default:
        return Achievement(
          id: id,
          title: 'Achievement',
          description: 'You earned an achievement!',
          icon: 'emoji_events',
          color: 'primary',
          points: 50,
        );
    }
  }

  // Show achievement notification
  Future<void> showAchievementNotification(Achievement achievement) async {
    await _notificationService.showAchievementNotification(
      title: achievement.title,
      description: achievement.description,
    );
  }

  // Calculate user level based on total points
  UserLevel calculateUserLevel(int totalPoints) {
    if (totalPoints < 500) {
      return UserLevel(level: 1, title: 'Beginner', nextLevelPoints: 500);
    } else if (totalPoints < 1500) {
      return UserLevel(level: 2, title: 'Student', nextLevelPoints: 1500);
    } else if (totalPoints < 3500) {
      return UserLevel(level: 3, title: 'Scholar', nextLevelPoints: 3500);
    } else if (totalPoints < 7500) {
      return UserLevel(level: 4, title: 'Expert', nextLevelPoints: 7500);
    } else if (totalPoints < 15000) {
      return UserLevel(level: 5, title: 'Master', nextLevelPoints: 15000);
    } else if (totalPoints < 30000) {
      return UserLevel(level: 6, title: 'Grandmaster', nextLevelPoints: 30000);
    } else {
      return UserLevel(level: 7, title: 'Legend', nextLevelPoints: null);
    }
  }

  // Get all available achievements
  List<Achievement> getAllAchievements() {
    return [
      _getAchievement('first_hour'),
      _getAchievement('study_warrior'),
      _getAchievement('study_master'),
      _getAchievement('three_day_streak'),
      _getAchievement('week_streak'),
      _getAchievement('month_streak'),
      _getAchievement('century_streak'),
      _getAchievement('flashcard_novice'),
      _getAchievement('flashcard_master'),
      _getAchievement('flashcard_legend'),
      _getAchievement('point_collector'),
      _getAchievement('point_master'),
      _getAchievement('first_completion'),
      _getAchievement('plan_master'),
    ];
  }

  // Calculate study consistency score (0-100)
  double calculateConsistencyScore(List<DateTime> studyDates) {
    if (studyDates.isEmpty) return 0.0;

    final now = DateTime.now();
    final thirtyDaysAgo = now.subtract(const Duration(days: 30));
    
    // Count study days in the last 30 days
    final recentStudyDays = studyDates
        .where((date) => date.isAfter(thirtyDaysAgo))
        .map((date) => DateTime(date.year, date.month, date.day))
        .toSet()
        .length;

    return (recentStudyDays / 30 * 100).clamp(0.0, 100.0);
  }

  // Get motivational message based on progress
  String getMotivationalMessage(UserStats stats) {
    final level = calculateUserLevel(stats.totalPoints);
    
    if (stats.currentStreak == 0) {
      return "Ready to start your learning journey? Let's build that study streak! 🚀";
    } else if (stats.currentStreak < 3) {
      return "Great start! Keep going to build your study habit! 💪";
    } else if (stats.currentStreak < 7) {
      return "You're on fire! ${stats.currentStreak} days strong! 🔥";
    } else if (stats.currentStreak < 30) {
      return "Amazing consistency! ${stats.currentStreak} days of dedication! ⭐";
    } else {
      return "Incredible! ${stats.currentStreak} days of pure dedication! You're a study legend! 🏆";
    }
  }
}

class Achievement {
  final String id;
  final String title;
  final String description;
  final String icon;
  final String color;
  final int points;

  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.points,
  });
}

class UserLevel {
  final int level;
  final String title;
  final int? nextLevelPoints;

  UserLevel({
    required this.level,
    required this.title,
    this.nextLevelPoints,
  });

  double getProgressToNextLevel(int currentPoints) {
    if (nextLevelPoints == null) return 1.0;
    
    final previousLevelPoints = _getPreviousLevelPoints(level);
    final pointsInCurrentLevel = currentPoints - previousLevelPoints;
    final pointsNeededForLevel = nextLevelPoints! - previousLevelPoints;
    
    return (pointsInCurrentLevel / pointsNeededForLevel).clamp(0.0, 1.0);
  }

  int _getPreviousLevelPoints(int level) {
    switch (level) {
      case 1: return 0;
      case 2: return 500;
      case 3: return 1500;
      case 4: return 3500;
      case 5: return 7500;
      case 6: return 15000;
      case 7: return 30000;
      default: return 0;
    }
  }
}
