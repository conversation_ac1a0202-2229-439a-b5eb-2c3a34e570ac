import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../controllers/user_controller.dart';
import '../../constants/app_constants.dart';

class ProgressScreen extends StatelessWidget {
  const ProgressScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Progress'),
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: () {
              // TODO: Show date range picker
            },
          ),
        ],
      ),
      body: Consumer<UserController>(
        builder: (context, userController, child) {
          final stats = userController.stats;
          
          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppDimensions.paddingL),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Overview Stats
                _buildOverviewStats(stats),
                
                const SizedBox(height: AppDimensions.paddingL),
                
                // Study Streak
                _buildStudyStreak(stats),
                
                const SizedBox(height: AppDimensions.paddingL),
                
                // Weekly Progress Chart
                _buildWeeklyProgress(),
                
                const SizedBox(height: AppDimensions.paddingL),
                
                // Achievements
                _buildAchievements(stats, userController),
                
                const SizedBox(height: AppDimensions.paddingL),
                
                // Subject Breakdown
                _buildSubjectBreakdown(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildOverviewStats(UserStats stats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Overview',
              style: AppTextStyles.headline3,
            ),
            const SizedBox(height: AppDimensions.paddingL),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Study Time',
                    '${(stats.totalStudyTimeMinutes / 60).toStringAsFixed(1)} hours',
                    Icons.access_time,
                    AppColors.primary,
                  ),
                ),
                const SizedBox(width: AppDimensions.paddingM),
                Expanded(
                  child: _buildStatCard(
                    'Total Points',
                    '${stats.totalPoints}',
                    Icons.stars,
                    AppColors.secondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Flashcards Reviewed',
                    '${stats.totalFlashcardsReviewed}',
                    Icons.style,
                    AppColors.info,
                  ),
                ),
                const SizedBox(width: AppDimensions.paddingM),
                Expanded(
                  child: _buildStatCard(
                    'Plans Completed',
                    '${stats.totalStudyPlansCompleted}',
                    Icons.check_circle,
                    AppColors.success,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: AppDimensions.iconL,
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            value,
            style: AppTextStyles.headline3.copyWith(color: color),
            textAlign: TextAlign.center,
          ),
          Text(
            title,
            style: AppTextStyles.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStudyStreak(UserStats stats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.local_fire_department,
                  color: AppColors.accent,
                  size: AppDimensions.iconM,
                ),
                const SizedBox(width: AppDimensions.paddingS),
                Text(
                  'Study Streak',
                  style: AppTextStyles.headline3,
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingL),
            Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        '${stats.currentStreak}',
                        style: AppTextStyles.headline1.copyWith(
                          color: AppColors.accent,
                          fontSize: 48,
                        ),
                      ),
                      Text(
                        'Current Streak',
                        style: AppTextStyles.bodyMedium,
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 1,
                  height: 60,
                  color: AppColors.surfaceVariant,
                ),
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        '${stats.longestStreak}',
                        style: AppTextStyles.headline2.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      Text(
                        'Best Streak',
                        style: AppTextStyles.bodyMedium,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),
            if (stats.lastStudyDate != null)
              Text(
                'Last study: ${_formatDate(stats.lastStudyDate!)}',
                style: AppTextStyles.bodySmall,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildWeeklyProgress() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'This Week',
              style: AppTextStyles.headline3,
            ),
            const SizedBox(height: AppDimensions.paddingL),
            // Placeholder for chart
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: AppColors.surfaceVariant,
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
              child: const Center(
                child: Text(
                  'Weekly Progress Chart\n(Coming Soon)',
                  textAlign: TextAlign.center,
                  style: AppTextStyles.bodyMedium,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAchievements(UserStats stats, UserController userController) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.emoji_events,
                  color: AppColors.accent,
                  size: AppDimensions.iconM,
                ),
                const SizedBox(width: AppDimensions.paddingS),
                Text(
                  'Achievements',
                  style: AppTextStyles.headline3,
                ),
                const Spacer(),
                Text(
                  '${stats.achievements.length}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingL),
            if (stats.achievements.isEmpty)
              Container(
                padding: const EdgeInsets.all(AppDimensions.paddingL),
                decoration: BoxDecoration(
                  color: AppColors.surfaceVariant,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
                child: const Center(
                  child: Text(
                    'Start studying to earn your first achievement!',
                    style: AppTextStyles.bodyMedium,
                  ),
                ),
              )
            else
              Wrap(
                spacing: AppDimensions.paddingS,
                runSpacing: AppDimensions.paddingS,
                children: stats.achievements.map((achievementId) {
                  final details = userController.getAchievementDetails(achievementId);
                  return _buildAchievementBadge(details['title']!, details['description']!);
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAchievementBadge(String title, String description) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingS),
      decoration: BoxDecoration(
        color: AppColors.accent.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.accent.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.emoji_events,
            color: AppColors.accent,
            size: AppDimensions.iconM,
          ),
          const SizedBox(height: AppDimensions.paddingXS),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSubjectBreakdown() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Subject Breakdown',
              style: AppTextStyles.headline3,
            ),
            const SizedBox(height: AppDimensions.paddingL),
            _buildSubjectItem('Mathematics', 45, AppColors.primary),
            _buildSubjectItem('Physics', 30, AppColors.secondary),
            _buildSubjectItem('Chemistry', 25, AppColors.accent),
          ],
        ),
      ),
    );
  }

  Widget _buildSubjectItem(String subject, int percentage, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingS),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                subject,
                style: AppTextStyles.bodyMedium,
              ),
              Text(
                '$percentage%',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.paddingXS),
          LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: AppColors.surfaceVariant,
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;
    
    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday';
    } else {
      return '$difference days ago';
    }
  }
}
